import * as fs from "node:fs";
import * as path from "node:path";
import {
  extractContactFromImage,
  organizeContactData,
} from "./apps/web/src/lib/ai/extract";
import { compressImage } from "./apps/web/src/lib/utils/image-compress";

/**
 * Simulate the exact Telegram image processing workflow
 * This replicates what happens when a user sends an image in Telegram
 */
async function simulateTelegramImageProcessing() {
  console.log("🔄 Starting Telegram image processing simulation...\n");

  // Step 1: Load the image (simulate Telegram file download)
  console.log("📥 Step 1: Loading image file...");
  const imagePath = process.argv[2]
    ? path.resolve(process.argv[2])
    : path.resolve(process.cwd(), "image.png");
  const imageBuffer = fs.readFileSync(imagePath);
  console.log(`   Original size: ${imageBuffer.length} bytes`);

  // Step 2: Compress the image (from handlers.ts line 198)
  console.log("\n🗜️  Step 2: Compressing image...");
  const compressedBuffer = await compressImage(imageBuffer);
  console.log(`   Compressed size: ${compressedBuffer.length} bytes`);
  console.log(
    `   Compression ratio: ${Math.round((1 - compressedBuffer.length / imageBuffer.length) * 100)}%`
  );

  // Step 3: Convert to base64 (from handlers.ts line 199)
  console.log("\n🔤 Step 3: Converting to base64...");
  const base64Image = compressedBuffer.toString("base64");
  console.log(`   Base64 length: ${base64Image.length} characters`);

  // Step 4: Extract contact information using AI (from handlers.ts line 201)
  console.log("\n🤖 Step 4: Extracting contact information with AI...");

  if (process.env.DRY_RUN === "1" || process.env.DRY_RUN === "true") {
    console.log("   DRY_RUN enabled: skipping network call to AI provider.");
    console.log("   Set DRY_RUN=0 and OPENROUTER_API_KEY to run live.");
    return {
      originalSize: imageBuffer.length,
      compressedSize: compressedBuffer.length,
      extractedText: "[DRY_RUN] No AI call performed",
      organizedData: {
        leadName: undefined,
        leadWebsite: undefined,
        leadPhone: undefined,
        leadTelegram: undefined,
        leadGeneralInfo: "No AI call performed",
        formattedPreview: "DRY_RUN: Skipped AI extraction and organization.",
      },
    } as any;
  }

  try {
    const extractedText = await extractContactFromImage(
      base64Image,
      "image/jpeg"
    );
    console.log("   Extracted text:");
    console.log("   ================");
    console.log(extractedText);
    console.log("   ================\n");

    // Step 5: Simulate session data (what would be stored in Convex)
    console.log("💾 Step 5: Creating session data...");
    const sessionData = [
      {
        type: "photo" as const,
        content: extractedText,
        timestamp: Date.now(),
      },
    ];
    console.log("   Session data created with extracted text");

    // Step 6: Organize contact data (from processSessionData in handlers.ts)
    console.log("\n📋 Step 6: Organizing contact data...");
    const salesRepName = "Test Sales Rep"; // Simulated sales rep
    const organizedData = await organizeContactData(sessionData, salesRepName);

    console.log("   Organized Contact Data:");
    console.log("   ======================");
    console.log(organizedData.formattedPreview);
    console.log("   ======================\n");

    // Step 7: Show what would be saved to database
    console.log("💿 Step 7: Contact data for database storage:");
    console.log("   Lead Name:", organizedData.leadName || "Not provided");
    console.log(
      "   Lead Website:",
      organizedData.leadWebsite || "Not provided"
    );
    console.log("   Lead Phone:", organizedData.leadPhone || "Not provided");
    console.log(
      "   Lead Telegram:",
      organizedData.leadTelegram || "Not provided"
    );
    console.log(
      "   Lead General Info:",
      organizedData.leadGeneralInfo || "Not provided"
    );

    console.log(
      "\n✅ Telegram image processing simulation completed successfully!"
    );

    return {
      originalSize: imageBuffer.length,
      compressedSize: compressedBuffer.length,
      extractedText,
      organizedData,
    };
  } catch (error) {
    console.error("❌ Error during AI processing:", error);
    throw error;
  }
}

// Run the simulation
simulateTelegramImageProcessing()
  .then((result) => {
    console.log("\n🎉 Simulation Results Summary:");
    console.log(
      `   Image compression: ${result.originalSize} → ${result.compressedSize} bytes`
    );
    console.log("   AI successfully extracted contact information");
    console.log("   Contact organized and ready for storage");
  })
  .catch((error) => {
    console.error("\n💥 Simulation failed:", error.message);
    process.exit(1);
  });
