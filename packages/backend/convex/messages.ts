import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

export const storeMessage = mutation({
  args: {
    telegramUserId: v.string(),
    salesRepId: v.optional(v.id("salesReps")),
    messageId: v.string(),
    messageType: v.union(
      v.literal("text"),
      v.literal("photo"),
      v.literal("document"),
      v.literal("command"),
      v.literal("callback_query"),
      v.literal("other")
    ),
    content: v.string(),
    fileId: v.optional(v.string()),
    sessionId: v.optional(v.id("contactSessions")),
    isPartOfContactSession: v.boolean(),
    rawTelegramData: v.string(),
  },
  returns: v.id("messages"),
  handler: async (ctx, args) =>
    await ctx.db.insert("messages", {
      telegramUserId: args.telegramUserId,
      salesRepId: args.salesRepId,
      messageId: args.messageId,
      messageType: args.messageType,
      content: args.content,
      fileId: args.fileId,
      sessionId: args.sessionId,
      isPartOfContactSession: args.isPartOfContactSession,
      timestamp: Date.now(),
      rawTelegramData: args.rawTelegramData,
    }),
});

const messageObjectValidator = v.object({
  _id: v.id("messages"),
  _creationTime: v.number(),
  telegramUserId: v.string(),
  salesRepId: v.optional(v.id("salesReps")),
  messageId: v.string(),
  messageType: v.union(
    v.literal("text"),
    v.literal("photo"),
    v.literal("document"),
    v.literal("command"),
    v.literal("callback_query"),
    v.literal("other")
  ),
  content: v.string(),
  fileId: v.optional(v.string()),
  sessionId: v.optional(v.id("contactSessions")),
  isPartOfContactSession: v.boolean(),
  timestamp: v.number(),
  rawTelegramData: v.string(),
});

export const getMessagesByUser = query({
  args: {
    telegramUserId: v.string(),
    limit: v.optional(v.number()),
  },
  returns: v.array(messageObjectValidator),
  handler: async (ctx, args) => {
    const limit = args.limit || 50;

    return await ctx.db
      .query("messages")
      .withIndex("by_telegram_user", (q) =>
        q.eq("telegramUserId", args.telegramUserId)
      )
      .order("desc")
      .take(limit);
  },
});

export const getMessagesBySalesRep = query({
  args: {
    salesRepId: v.id("salesReps"),
    limit: v.optional(v.number()),
  },
  returns: v.array(messageObjectValidator),
  handler: async (ctx, args) => {
    const limit = args.limit || 100;

    return await ctx.db
      .query("messages")
      .withIndex("by_sales_rep", (q) => q.eq("salesRepId", args.salesRepId))
      .order("desc")
      .take(limit);
  },
});

export const getSessionMessages = query({
  args: { sessionId: v.id("contactSessions") },
  returns: v.array(messageObjectValidator),
  handler: async (ctx, args) =>
    await ctx.db
      .query("messages")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .order("asc")
      .collect(),
});

export const getAllMessages = query({
  args: {
    limit: v.optional(v.number()),
    startTime: v.optional(v.number()),
    endTime: v.optional(v.number()),
  },
  returns: v.array(messageObjectValidator),
  handler: async (ctx, args) => {
    const limit = args.limit || 100;

    let query = ctx.db.query("messages").withIndex("by_timestamp");

    if (args.startTime && args.endTime) {
      query = query.filter((q) =>
        q.and(
          q.gte(q.field("timestamp"), args.startTime!),
          q.lte(q.field("timestamp"), args.endTime!)
        )
      );
    }

    return await query.order("desc").take(limit);
  },
});

export const getMessageStats = query({
  args: {},
  returns: v.object({
    totalMessages: v.number(),
    messagesByType: v.record(v.string(), v.number()),
    uniqueUsers: v.number(),
    messagesLast24h: v.number(),
    messagesInSessions: v.number(),
  }),
  handler: async (ctx, args) => {
    const allMessages = await ctx.db.query("messages").collect();

    const messagesByType: Record<string, number> = {};
    const uniqueUsers = new Set<string>();
    let messagesLast24h = 0;
    let messagesInSessions = 0;

    const last24h = Date.now() - 24 * 60 * 60 * 1000;

    allMessages.forEach((msg) => {
      messagesByType[msg.messageType] =
        (messagesByType[msg.messageType] || 0) + 1;
      uniqueUsers.add(msg.telegramUserId);

      if (msg.timestamp > last24h) {
        messagesLast24h++;
      }

      if (msg.isPartOfContactSession) {
        messagesInSessions++;
      }
    });

    return {
      totalMessages: allMessages.length,
      messagesByType,
      uniqueUsers: uniqueUsers.size,
      messagesLast24h,
      messagesInSessions,
    };
  },
});
