import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

export const createOrUpdateSalesRep = mutation({
  args: {
    telegramId: v.string(),
    firstName: v.string(),
    lastName: v.optional(v.string()),
    username: v.optional(v.string()),
  },
  returns: v.id("salesReps"),
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("salesReps")
      .withIndex("by_telegram_id", (q) => q.eq("telegramId", args.telegramId))
      .first();

    if (existing) {
      await ctx.db.patch(existing._id, {
        firstName: args.firstName,
        lastName: args.lastName,
        username: args.username,
        isActive: true,
      });
      return existing._id;
    }

    return await ctx.db.insert("salesReps", {
      telegramId: args.telegramId,
      firstName: args.firstName,
      lastName: args.lastName,
      username: args.username,
      isActive: true,
    });
  },
});

export const getSalesRepByTelegramId = query({
  args: { telegramId: v.string() },
  returns: v.union(
    v.object({
      _id: v.id("salesReps"),
      _creationTime: v.number(),
      telegramId: v.string(),
      firstName: v.string(),
      lastName: v.optional(v.string()),
      username: v.optional(v.string()),
      isActive: v.boolean(),
    }),
    v.null()
  ),
  handler: async (ctx, args) =>
    await ctx.db
      .query("salesReps")
      .withIndex("by_telegram_id", (q) => q.eq("telegramId", args.telegramId))
      .first(),
});

export const getAllSalesReps = query({
  args: {},
  returns: v.array(
    v.object({
      _id: v.id("salesReps"),
      _creationTime: v.number(),
      telegramId: v.string(),
      firstName: v.string(),
      lastName: v.optional(v.string()),
      username: v.optional(v.string()),
      isActive: v.boolean(),
    })
  ),
  handler: async (ctx) =>
    await ctx.db.query("salesReps").order("desc").collect(),
});
