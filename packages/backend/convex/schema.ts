import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  todos: defineTable({
    text: v.string(),
    completed: v.boolean(),
  }),
  salesReps: defineTable({
    telegramId: v.string(),
    firstName: v.string(),
    lastName: v.optional(v.string()),
    username: v.optional(v.string()),
    isActive: v.boolean(),
  }).index("by_telegram_id", ["telegramId"]),
  messages: defineTable({
    telegramUserId: v.string(),
    salesRepId: v.optional(v.id("salesReps")),
    messageId: v.string(),
    messageType: v.union(
      v.literal("text"),
      v.literal("photo"),
      v.literal("document"),
      v.literal("command"),
      v.literal("callback_query"),
      v.literal("other")
    ),
    content: v.string(),
    fileId: v.optional(v.string()),
    sessionId: v.optional(v.id("contactSessions")),
    isPartOfContactSession: v.boolean(),
    timestamp: v.number(),
    rawTelegramData: v.string(),
  })
    .index("by_telegram_user", ["telegramUserId"])
    .index("by_sales_rep", ["salesRepId"])
    .index("by_session", ["sessionId"])
    .index("by_timestamp", ["timestamp"]),
  contacts: defineTable({
    salesRepId: v.id("salesReps"),

    // Legacy fields (for backward compatibility)
    leadName: v.optional(v.string()),
    leadWebsite: v.optional(v.string()),
    leadPhone: v.optional(v.string()),
    leadTelegram: v.optional(v.string()),
    leadGeneralInfo: v.optional(v.string()),

    // Professional Information
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    middleName: v.optional(v.string()),
    jobTitle: v.optional(v.string()),
    department: v.optional(v.string()),
    companyName: v.optional(v.string()),
    companyWebsite: v.optional(v.string()),
    industry: v.optional(v.string()),

    // Contact Details
    emailWork: v.optional(v.string()),
    emailPersonal: v.optional(v.string()),
    phoneMobile: v.optional(v.string()),
    phoneOffice: v.optional(v.string()),
    phoneFax: v.optional(v.string()),
    linkedinUrl: v.optional(v.string()),
    telegramHandle: v.optional(v.string()),
    whatsappNumber: v.optional(v.string()),
    twitterHandle: v.optional(v.string()),
    instagramHandle: v.optional(v.string()),

    // Location
    addressStreet: v.optional(v.string()),
    addressCity: v.optional(v.string()),
    addressState: v.optional(v.string()),
    addressZip: v.optional(v.string()),
    addressCountry: v.optional(v.string()),

    // Metadata
    leadSource: v.optional(v.string()),
    leadScore: v.optional(v.number()),
    dataQuality: v.optional(v.union(v.literal("high"), v.literal("medium"), v.literal("low"))),
    tags: v.optional(v.array(v.string())),
    notes: v.optional(v.string()),

    // System fields
    rawData: v.string(),
    extractedAt: v.number(),
    isConfirmed: v.boolean(),
    sessionId: v.optional(v.id("contactSessions")),
  })
    .index("by_sales_rep", ["salesRepId"])
    .index("by_session", ["sessionId"])
    .index("by_email_work", ["emailWork"])
    .index("by_phone_mobile", ["phoneMobile"])
    .index("by_company", ["companyName"])
    .index("by_data_quality", ["dataQuality"]),
  contactSessions: defineTable({
    telegramUserId: v.string(),
    salesRepId: v.id("salesReps"),
    sessionData: v.array(
      v.object({
        messageId: v.string(),
        type: v.union(
          v.literal("text"),
          v.literal("photo"),
          v.literal("document")
        ),
        content: v.string(),
        timestamp: v.number(),
        imageStorageId: v.optional(v.id("_storage")), // Convex file storage ID
        mimeType: v.optional(v.string()), // Image MIME type
      })
    ),
    isActive: v.boolean(),
    startedAt: v.number(),
    lastActivityAt: v.number(),
  })
    .index("by_telegram_user", ["telegramUserId"])
    .index("by_active", ["isActive"]),
});
