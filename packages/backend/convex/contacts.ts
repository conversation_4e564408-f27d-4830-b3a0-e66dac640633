import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

export const createContact = mutation({
  args: {
    salesRepId: v.id("salesReps"),

    // Legacy fields
    leadName: v.optional(v.string()),
    leadWebsite: v.optional(v.string()),
    leadPhone: v.optional(v.string()),
    leadTelegram: v.optional(v.string()),
    leadGeneralInfo: v.optional(v.string()),

    // Professional Information
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    middleName: v.optional(v.string()),
    jobTitle: v.optional(v.string()),
    department: v.optional(v.string()),
    companyName: v.optional(v.string()),
    companyWebsite: v.optional(v.string()),
    industry: v.optional(v.string()),

    // Contact Details
    emailWork: v.optional(v.string()),
    emailPersonal: v.optional(v.string()),
    phoneMobile: v.optional(v.string()),
    phoneOffice: v.optional(v.string()),
    phoneFax: v.optional(v.string()),
    linkedinUrl: v.optional(v.string()),
    telegramHandle: v.optional(v.string()),
    whatsappNumber: v.optional(v.string()),
    twitterHandle: v.optional(v.string()),
    instagramHandle: v.optional(v.string()),

    // Location
    addressStreet: v.optional(v.string()),
    addressCity: v.optional(v.string()),
    addressState: v.optional(v.string()),
    addressZip: v.optional(v.string()),
    addressCountry: v.optional(v.string()),

    // Metadata
    leadSource: v.optional(v.string()),
    leadScore: v.optional(v.number()),
    dataQuality: v.optional(v.union(v.literal("high"), v.literal("medium"), v.literal("low"))),
    tags: v.optional(v.array(v.string())),
    notes: v.optional(v.string()),

    // System fields
    rawData: v.string(),
    sessionId: v.optional(v.id("contactSessions")),
  },
  returns: v.id("contacts"),
  handler: async (ctx, args) =>
    await ctx.db.insert("contacts", {
      salesRepId: args.salesRepId,

      // Legacy fields
      leadName: args.leadName,
      leadWebsite: args.leadWebsite,
      leadPhone: args.leadPhone,
      leadTelegram: args.leadTelegram,
      leadGeneralInfo: args.leadGeneralInfo,

      // Professional Information
      firstName: args.firstName,
      lastName: args.lastName,
      middleName: args.middleName,
      jobTitle: args.jobTitle,
      department: args.department,
      companyName: args.companyName,
      companyWebsite: args.companyWebsite,
      industry: args.industry,

      // Contact Details
      emailWork: args.emailWork,
      emailPersonal: args.emailPersonal,
      phoneMobile: args.phoneMobile,
      phoneOffice: args.phoneOffice,
      phoneFax: args.phoneFax,
      linkedinUrl: args.linkedinUrl,
      telegramHandle: args.telegramHandle,
      whatsappNumber: args.whatsappNumber,
      twitterHandle: args.twitterHandle,
      instagramHandle: args.instagramHandle,

      // Location
      addressStreet: args.addressStreet,
      addressCity: args.addressCity,
      addressState: args.addressState,
      addressZip: args.addressZip,
      addressCountry: args.addressCountry,

      // Metadata
      leadSource: args.leadSource,
      leadScore: args.leadScore,
      dataQuality: args.dataQuality,
      tags: args.tags,
      notes: args.notes,

      // System fields
      rawData: args.rawData,
      extractedAt: Date.now(),
      isConfirmed: false,
      sessionId: args.sessionId,
    }),
});

export const confirmContact = mutation({
  args: { contactId: v.id("contacts") },
  returns: v.null(),
  handler: async (ctx, args) => {
    await ctx.db.patch(args.contactId, { isConfirmed: true });
    return null;
  },
});

export const getContactsBySalesRep = query({
  args: { salesRepId: v.id("salesReps") },
  returns: v.array(
    v.object({
      _id: v.id("contacts"),
      _creationTime: v.number(),
      salesRepId: v.id("salesReps"),

      // Legacy fields
      leadName: v.optional(v.string()),
      leadWebsite: v.optional(v.string()),
      leadPhone: v.optional(v.string()),
      leadTelegram: v.optional(v.string()),
      leadGeneralInfo: v.optional(v.string()),

      // Professional Information
      firstName: v.optional(v.string()),
      lastName: v.optional(v.string()),
      middleName: v.optional(v.string()),
      jobTitle: v.optional(v.string()),
      department: v.optional(v.string()),
      companyName: v.optional(v.string()),
      companyWebsite: v.optional(v.string()),
      industry: v.optional(v.string()),

      // Contact Details
      emailWork: v.optional(v.string()),
      emailPersonal: v.optional(v.string()),
      phoneMobile: v.optional(v.string()),
      phoneOffice: v.optional(v.string()),
      phoneFax: v.optional(v.string()),
      linkedinUrl: v.optional(v.string()),
      telegramHandle: v.optional(v.string()),
      whatsappNumber: v.optional(v.string()),
      twitterHandle: v.optional(v.string()),
      instagramHandle: v.optional(v.string()),

      // Location
      addressStreet: v.optional(v.string()),
      addressCity: v.optional(v.string()),
      addressState: v.optional(v.string()),
      addressZip: v.optional(v.string()),
      addressCountry: v.optional(v.string()),

      // Metadata
      leadSource: v.optional(v.string()),
      leadScore: v.optional(v.number()),
      dataQuality: v.optional(v.union(v.literal("high"), v.literal("medium"), v.literal("low"))),
      tags: v.optional(v.array(v.string())),
      notes: v.optional(v.string()),

      // System fields
      rawData: v.string(),
      extractedAt: v.number(),
      isConfirmed: v.boolean(),
      sessionId: v.optional(v.id("contactSessions")),
    })
  ),
  handler: async (ctx, args) =>
    await ctx.db
      .query("contacts")
      .withIndex("by_sales_rep", (q) => q.eq("salesRepId", args.salesRepId))
      .order("desc")
      .collect(),
});

export const getAllContactsWithSalesRep = query({
  args: {},
  returns: v.array(
    v.object({
      _id: v.id("contacts"),
      _creationTime: v.number(),
      salesRepId: v.id("salesReps"),
      leadName: v.optional(v.string()),
      leadWebsite: v.optional(v.string()),
      leadPhone: v.optional(v.string()),
      leadTelegram: v.optional(v.string()),
      leadGeneralInfo: v.optional(v.string()),
      rawData: v.string(),
      extractedAt: v.number(),
      isConfirmed: v.boolean(),
      sessionId: v.optional(v.id("contactSessions")),
      salesRep: v.union(
        v.object({
          _id: v.id("salesReps"),
          _creationTime: v.number(),
          telegramId: v.string(),
          firstName: v.string(),
          lastName: v.optional(v.string()),
          username: v.optional(v.string()),
          isActive: v.boolean(),
        }),
        v.null()
      ),
    })
  ),
  handler: async (ctx, args) => {
    const contacts = await ctx.db.query("contacts").order("desc").collect();

    const contactsWithSalesRep = await Promise.all(
      contacts.map(async (contact) => {
        const salesRep = await ctx.db.get(contact.salesRepId);
        return {
          ...contact,
          salesRep,
        };
      })
    );

    return contactsWithSalesRep;
  },
});

export const deleteContact = mutation({
  args: { contactId: v.id("contacts") },
  returns: v.null(),
  handler: async (ctx, args) => {
    await ctx.db.delete(args.contactId);
    return null;
  },
});
