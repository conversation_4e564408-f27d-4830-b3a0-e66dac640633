import { v } from "convex/values";
import { mutation, query, action, internalMutation } from "./_generated/server";
import { internal } from "./_generated/api";
import type { Id } from "./_generated/dataModel";

export const createSession = mutation({
  args: {
    telegramUserId: v.string(),
    salesRepId: v.id("salesReps"),
  },
  returns: v.id("contactSessions"),
  handler: async (ctx, args) => {
    await ctx.db
      .query("contactSessions")
      .withIndex("by_telegram_user", (q) =>
        q.eq("telegramUserId", args.telegramUserId)
      )
      .collect()
      .then((sessions) =>
        Promise.all(
          sessions
            .filter((session) => session.isActive)
            .map((session) => ctx.db.patch(session._id, { isActive: false }))
        )
      );

    return await ctx.db.insert("contactSessions", {
      telegramUserId: args.telegramUserId,
      salesRepId: args.salesRepId,
      sessionData: [],
      isActive: true,
      startedAt: Date.now(),
      lastActivityAt: Date.now(),
    });
  },
});

// Action to handle image storage and session data
export const addSessionData = action({
  args: {
    sessionId: v.id("contactSessions"),
    messageId: v.string(),
    type: v.union(v.literal("text"), v.literal("photo"), v.literal("document")),
    content: v.string(),
    imageData: v.optional(v.string()), // Base64 image data for storage
    mimeType: v.optional(v.string()),
  },
  returns: v.object({
    messageId: v.string(),
    type: v.union(v.literal("text"), v.literal("photo"), v.literal("document")),
    content: v.string(),
    timestamp: v.number(),
    imageStorageId: v.optional(v.id("_storage")),
    mimeType: v.optional(v.string()),
  }),
  handler: async (ctx, args): Promise<{
    messageId: string;
    type: "text" | "photo" | "document";
    content: string;
    timestamp: number;
    imageStorageId?: Id<"_storage">;
    mimeType?: string;
  }> => {
    let imageStorageId = undefined;

    // If image data is provided, store it using Convex file storage
    if (args.imageData && args.mimeType) {
      try {
        // Convert base64 to ArrayBuffer
        const base64Data = args.imageData.replace(/^data:[^;]+;base64,/, '');
        const binaryString = atob(base64Data);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }

        // Create blob and store it
        const blob = new Blob([bytes], { type: args.mimeType });
        imageStorageId = await ctx.storage.store(blob);
      } catch (error) {
        console.error("Failed to store image:", error);
        throw new Error("Failed to store image data");
      }
    }

    // Call the mutation to store session data
    return await ctx.runMutation(internal.sessions.addSessionDataMutation, {
      sessionId: args.sessionId,
      messageId: args.messageId,
      type: args.type,
      content: args.content,
      imageStorageId,
      mimeType: args.mimeType,
    });
  },
});

// Internal mutation to store session data in database
export const addSessionDataMutation = internalMutation({
  args: {
    sessionId: v.id("contactSessions"),
    messageId: v.string(),
    type: v.union(v.literal("text"), v.literal("photo"), v.literal("document")),
    content: v.string(),
    imageStorageId: v.optional(v.id("_storage")),
    mimeType: v.optional(v.string()),
  },
  returns: v.object({
    messageId: v.string(),
    type: v.union(v.literal("text"), v.literal("photo"), v.literal("document")),
    content: v.string(),
    timestamp: v.number(),
    imageStorageId: v.optional(v.id("_storage")),
    mimeType: v.optional(v.string()),
  }),
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!(session && session.isActive)) {
      throw new Error("Session not found or not active");
    }

    const newData = {
      messageId: args.messageId,
      type: args.type,
      content: args.content,
      timestamp: Date.now(),
      ...(args.imageStorageId && { imageStorageId: args.imageStorageId }),
      ...(args.mimeType && { mimeType: args.mimeType }),
    };

    const updatedSessionData = [...session.sessionData, newData];

    await ctx.db.patch(args.sessionId, {
      sessionData: updatedSessionData,
      lastActivityAt: Date.now(),
    });

    return newData;
  },
});

export const getActiveSession = query({
  args: { telegramUserId: v.string() },
  returns: v.union(
    v.object({
      _id: v.id("contactSessions"),
      _creationTime: v.number(),
      telegramUserId: v.string(),
      salesRepId: v.id("salesReps"),
      sessionData: v.array(
        v.object({
          messageId: v.string(),
          type: v.union(
            v.literal("text"),
            v.literal("photo"),
            v.literal("document")
          ),
          content: v.string(),
          timestamp: v.number(),
          imageStorageId: v.optional(v.id("_storage")),
          mimeType: v.optional(v.string()),
        })
      ),
      isActive: v.boolean(),
      startedAt: v.number(),
      lastActivityAt: v.number(),
    }),
    v.null()
  ),
  handler: async (ctx, args) =>
    await ctx.db
      .query("contactSessions")
      .withIndex("by_telegram_user", (q) =>
        q.eq("telegramUserId", args.telegramUserId)
      )
      .filter((q) => q.eq(q.field("isActive"), true))
      .first(),
});

export const closeSession = mutation({
  args: { sessionId: v.id("contactSessions") },
  returns: v.null(),
  handler: async (ctx, args) => {
    await ctx.db.patch(args.sessionId, { isActive: false });
    return null;
  },
});

export const getInactiveSessions = query({
  args: { olderThanMinutes: v.number() },
  returns: v.array(
    v.object({
      _id: v.id("contactSessions"),
      _creationTime: v.number(),
      telegramUserId: v.string(),
      salesRepId: v.id("salesReps"),
      sessionData: v.array(
        v.object({
          messageId: v.string(),
          type: v.union(
            v.literal("text"),
            v.literal("photo"),
            v.literal("document")
          ),
          content: v.string(),
          timestamp: v.number(),
          imageStorageId: v.optional(v.id("_storage")),
          mimeType: v.optional(v.string()),
        })
      ),
      isActive: v.boolean(),
      startedAt: v.number(),
      lastActivityAt: v.number(),
    })
  ),
  handler: async (ctx, args) => {
    const cutoffTime = Date.now() - args.olderThanMinutes * 60 * 1000;

    return await ctx.db
      .query("contactSessions")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .filter((q) => q.lt(q.field("lastActivityAt"), cutoffTime))
      .collect();
  },
});

export const getImageUrl = query({
  args: { storageId: v.id("_storage") },
  returns: v.union(v.string(), v.null()),
  handler: async (ctx, args) => {
    return await ctx.storage.getUrl(args.storageId);
  },
});
