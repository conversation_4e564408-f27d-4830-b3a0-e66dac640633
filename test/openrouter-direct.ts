import * as fs from "node:fs";
import * as path from "node:path";

async function callOpenRouterChat(model: string, messages: any[]) {
  const apiKey = process.env.OPENROUTER_API_KEY;
  if (!api<PERSON>ey) throw new Error("OPENROUTER_API_KEY not set");

  const res = await fetch("https://openrouter.ai/api/v1/chat/completions", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${apiKey}`,
      // Optional but recommended by OpenRouter
      "HTTP-Referer": "https://buddycontact.local/test",
      "X-Title": "BuddyContact Test",
    },
    body: JSON.stringify({
      model,
      messages,
    }),
  });

  const text = await res.text();
  console.log("[openrouter] status:", res.status, res.statusText);
  try {
    const json = JSON.parse(text);
    console.log("[openrouter] json:", JSON.stringify(json, null, 2).slice(0, 4000));
    return json;
  } catch {
    console.log("[openrouter] raw:", text.slice(0, 4000));
    return null;
  }
}

async function main() {
  const imagePath = process.argv[2]
    ? path.resolve(process.argv[2])
    : path.resolve(process.cwd(), "image.png");
  const buf = fs.existsSync(imagePath) ? fs.readFileSync(imagePath) : null;
  const base64 = buf ? buf.toString("base64") : null;

  console.log("== Test 1: text-only sanity (openai/gpt-4o-mini) ==");
  await callOpenRouterChat("openai/gpt-4o-mini", [
    { role: "user", content: [{ type: "text", text: "Say OK if you can read this." }] },
  ]);

  console.log("\n== Test 2: x-ai/grok-4-fast text-only ==");
  await callOpenRouterChat("x-ai/grok-4-fast", [
    { role: "user", content: [{ type: "text", text: "Say GROK OK if you can read this." }] },
  ]);

  if (base64) {
    console.log("\n== Test 3: gemini 2.x multimodal via data URL ==");
    const mime = "image/png";
    await callOpenRouterChat(process.env.OPENROUTER_GEMINI_MODEL || "google/gemini-2.0-flash", [
      {
        role: "user",
        content: [
          { type: "text", text: "Extract all visible text from this image. Reply with the raw text only." },
          { type: "image_url", image_url: { url: `data:${mime};base64,${base64}` } },
        ],
      },
    ]);
  } else {
    console.log("(No image found at", imagePath, ") — skipping Test 3");
  }
}

main().catch((err) => {
  console.log("[openrouter-direct] error", err);
  process.exit(1);
});

