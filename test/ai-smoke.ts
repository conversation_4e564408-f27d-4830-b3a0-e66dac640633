import { createOpenAI } from "@ai-sdk/openai";
import { generateText } from "ai";

async function main() {
  const apiKey = process.env.OPENROUTER_API_KEY;
  if (!apiKey) {
    console.log("Missing OPENROUTER_API_KEY");
    process.exit(1);
  }
  const openrouter = createOpenAI({ apiKey, baseURL: "https://openrouter.ai/api/v1" });
  console.log("[ai-smoke] calling text-only model: openai/gpt-4o-mini");
  const { text } = await generateText({
    model: openrouter("openai/gpt-4o-mini"),
    messages: [
      { role: "user", content: [ { type: "text", text: "Say OK if you can read this." } ] }
    ],
  });
  console.log("[ai-smoke] response:", text);
}

main().catch(err => {
  console.log("[ai-smoke] error", {
    name: (err as any)?.name,
    message: (err as any)?.message,
    status: (err as any)?.status,
    code: (err as any)?.code,
    data: (err as any)?.data,
    response: (err as any)?.response,
  });
  process.exit(1);
});

