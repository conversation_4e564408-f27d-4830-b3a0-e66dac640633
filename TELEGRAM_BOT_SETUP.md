# 🤖 Telegram Bot Contact Management System

## Overview

This Telegram bot helps sales representatives collect and organize contact information from leads. It uses AI to extract data from images, organize information, and export contacts to Excel sheets.

## Features

- ✅ **Comprehensive Message Logging**: Every message sent to the bot is stored in Convex
- ✅ **Multi-format Input Support**: Text, images, business cards, QR codes, documents
- ✅ **AI-Powered Extraction**: Uses OpenRouter + Gemini 2.0 Flash for intelligent data processing
- ✅ **Image Compression**: Automatic image optimization before storage
- ✅ **Session Management**: 3-second timeout with confirmation prompts
- ✅ **Structured Contact Preview**: Organized format for all contact data
- ✅ **Excel Export**: Downloadable spreadsheets with complete contact information
- ✅ **Sales Rep Tracking**: Associates all contacts with the appropriate sales representative

## Setup Instructions

### 1. Create a Telegram Bot

1. Message [@BotFather](https://t.me/botfather) on Telegram
2. Send `/newbot` command
3. Follow the instructions to create your bot
4. Save the bot token - you'll need it for environment variables

### 2. Get OpenRouter API Key

1. Sign up at [OpenRouter](https://openrouter.ai)
2. Get your API key from the dashboard
3. Make sure you have credits for the Gemini model

### 3. Environment Variables

Create your local `.env` file in the `apps/web` directory (this file is ignored by git for security):

```bash
# Convex
NEXT_PUBLIC_CONVEX_URL=https://your-convex-deployment.convex.cloud

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_bot_token_from_botfather

# AI Configuration (OpenRouter)
OPENROUTER_API_KEY=your_openrouter_api_key

# Production Webhook URL (for production deployment)
WEBHOOK_URL=https://contact.buddytools.org/api/telegram/webhook
```

**For production**: Create `.env.production` with the same values (also ignored by git).

### 4. Deploy Your App

1. Deploy your Next.js app to your hosting platform (configured for `contact.buddytools.org`)
2. Make sure your domain is publicly accessible for webhooks
3. Configure your hosting platform with the environment variables from `.env.production`

### 5. Configure Webhook

#### Option 1: Using Package Scripts (Recommended)
```bash
cd apps/web

# Set up webhook (uses contact.buddytools.org by default)
bun run webhook:setup

# Check webhook status
bun run webhook:info

# Register commands only (without webhook)
bun run webhook:commands

# Delete webhook (if needed)
bun run webhook:delete
```

#### Option 2: Manual Setup
```bash
# Set environment variables
export TELEGRAM_BOT_TOKEN=your_bot_token
export WEBHOOK_URL=https://contact.buddytools.org/api/telegram/webhook

# Run the setup script
node apps/web/src/scripts/setup-telegram-webhook.js set
```

#### Complete Production Deployment
```bash
cd apps/web
bun run deploy:prod  # Builds and sets up webhook automatically
```

### 6. Initialize Convex

```bash
cd packages/backend
bun run dev:setup
```

## Bot Commands

- `/start` - Welcome message and instructions
- `/contact` - Start collecting contact information
- `/info` - Learn how the bot works (detailed explanation)
- `/help` - Show help information

## How It Works

### Contact Collection Flow

1. **Start Session**: User sends `/contact` command
2. **Data Collection**: Bot accepts multiple inputs:
   - Text messages with contact details
   - Photos of business cards (OCR extraction)
   - Screenshots with contact information
   - Documents with contact data
   - QR codes
3. **Timeout Check**: After 3 seconds of inactivity, bot asks "Are you done?"
4. **AI Processing**: Gemini processes all collected data and organizes it
5. **Preview**: Bot shows structured contact preview
6. **Confirmation**: User confirms or cancels the contact
7. **Storage**: Confirmed contacts are saved to Convex

### Message Logging

Every interaction is logged in the `messages` table:
- Message content and type
- Telegram user information
- Associated session (if in contact flow)
- Raw Telegram data for debugging
- Timestamps for audit trails

### Data Structure

**Organized Output Format:**
```
📋 Contact Preview:

Sales Person: [Name of the sales rep]
Lead Name: [Extracted/provided name]
Lead Website: [Extracted website/URL]
Lead General Information: [Additional details, company, role, notes]
Phone: [Formatted phone number]
Telegram: [Telegram handle]
```

## Excel Export

### Automatic Export
- **GET** `/api/export/excel` - Export all confirmed contacts
- **GET** `/api/export/excel?salesRepId=ID` - Export contacts for specific sales rep
- **GET** `/api/export/excel?includeUnconfirmed=true` - Include unconfirmed contacts

### Custom Export
- **POST** `/api/export/excel` with `contactIds` array - Export specific contacts

### Excel Columns
- Sales Person
- Lead Name
- Lead Website
- General Information
- Phone
- Telegram Handle
- Date Added
- Confirmed (Yes/No)

## Database Schema

### Tables Created
- `messages` - All bot interactions
- `salesReps` - Sales representative information
- `contacts` - Processed contact information
- `contactSessions` - Active collection sessions

## AI Configuration

The system uses:
- **Provider**: OpenRouter
- **Model**: `google/gemini-2.0-flash-exp`
- **Purpose**: Image OCR, text extraction, and data organization

## Troubleshooting

### Common Issues

1. **Webhook not receiving updates**
   - Check if `contact.buddytools.org` is publicly accessible
   - Verify HTTPS is properly configured for the domain
   - Run `bun run webhook:setup` to re-register the webhook
   - Check webhook status with `bun run webhook:info`

2. **AI extraction errors**
   - Check OpenRouter API key and credits
   - Verify the model is available

3. **Image processing fails**
   - Ensure Sharp is properly installed
   - Check file permissions for temporary files

### Debug Commands

Check webhook status:
```bash
curl https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getWebhookInfo
```

Delete webhook (for testing locally):
```bash
node apps/web/src/scripts/setup-telegram-webhook.js delete
```

## API Endpoints

- `POST /api/telegram/webhook` - Telegram webhook handler
- `GET /api/telegram/webhook` - Webhook status check
- `GET /api/export/excel` - Excel export
- `POST /api/export/excel` - Custom Excel export

## Security Notes

- Bot token should be kept secret
- All environment variables should be properly secured
- Consider rate limiting for production use
- Webhook URL should use HTTPS
- OpenRouter API key should be protected

## Production Deployment Summary

**Domain**: `contact.buddytools.org`

**Required Files**:
- Create `.env.production` locally with your production values (NOT committed to git for security)
- Environment variables configured on your hosting platform

**Deployment Steps**:
1. Deploy to hosting platform with domain `contact.buddytools.org`
2. Set environment variables from your local `.env` file
3. Run: `cd apps/web && bun run deploy:prod` (sets webhook + registers commands)
4. Test with `/start` command in Telegram

**Available Commands After Deployment**:
- `/start` - Welcome and introduction
- `/contact` - Start collecting contacts
- `/info` - Detailed how-it-works explanation
- `/help` - Quick usage help

**Webhook URL**: `https://contact.buddytools.org/api/telegram/webhook`

## Support

The bot includes comprehensive error handling and logging. Check your Convex dashboard for stored messages and contact data. All interactions are logged for debugging and analytics.

---

🎉 **Your Telegram bot is now ready for production at contact.buddytools.org!**