{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port=3001", "build": "next build", "start": "next start", "webhook:setup": "node src/scripts/setup-telegram-webhook.js set", "webhook:info": "curl https://api.telegram.org/bot$TELEGRAM_BOT_TOKEN/getWebhookInfo", "webhook:delete": "node src/scripts/setup-telegram-webhook.js delete", "webhook:commands": "node src/scripts/setup-telegram-webhook.js commands", "deploy:prod": "npm run build && npm run webhook:setup", "test:webhook": "tsx src/scripts/test/test-webhook-status.ts", "test:send": "tsx src/scripts/test/test-send-message.ts", "test:updates": "tsx src/scripts/test/test-get-updates.ts", "test:local": "tsx src/scripts/test/test-local-bot.ts", "test:endpoint": "tsx src/scripts/test/test-webhook-endpoint.ts", "test:all": "npm run test:webhook && npm run test:updates"}, "dependencies": {"@BuddyContact/backend": "workspace:*", "@ai-sdk/google": "^2.0.17", "@ai-sdk/openai": "^2.0.36", "@tanstack/react-form": "^1.12.3", "ai": "^5.0.54", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.27.0", "exceljs": "^4.4.0", "grammy": "^1.38.2", "lucide-react": "^0.487.0", "next": "15.5.0", "next-themes": "^0.4.6", "openai": "^5.23.0", "radix-ui": "^1.4.2", "react": "19.1.0", "react-dom": "19.1.0", "sharp": "^0.34.4", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "zod": "^4.0.2"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/node": "^20", "@types/react": "~19.1.10", "@types/react-dom": "^19", "@types/sharp": "^0.32.0", "tailwindcss": "^4.1.10", "tsx": "^4.20.6", "typescript": "^5"}}