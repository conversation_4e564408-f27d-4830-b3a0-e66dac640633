"use client";

import { ConvexProvider, ConvexReactClient } from "convex/react";
import { ThemeProvider } from "./theme-provider";
import { Toaster } from "./ui/sonner";

const convexUrl =
  process.env.NEXT_PUBLIC_CONVEX_URL?.trim() ||
  "https://dummy-url-for-build.convex.cloud";
const convex = new ConvexReactClient(convexUrl);

export default function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      disableTransitionOnChange
      enableSystem
    >
      <ConvexProvider client={convex}>{children}</ConvexProvider>
      <Toaster richColors />
    </ThemeProvider>
  );
}
