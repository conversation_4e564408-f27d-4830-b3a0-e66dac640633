/**
 * Webhook logging utilities for safely logging Telegram updates without exposing PII
 */

interface TelegramUpdate {
  update_id?: number;
  message?: {
    message_id?: number;
    from?: {
      id?: number;
      is_bot?: boolean;
      first_name?: string;
      last_name?: string;
      username?: string;
    };
    chat?: {
      id?: number;
      type?: string;
      username?: string;
    };
    date?: number;
    text?: string;
    photo?: any[];
    document?: any;
    caption?: string;
  };
  callback_query?: {
    id?: string;
    from?: any;
    data?: string;
  };
}

/**
 * Redacts sensitive information from a Telegram update for safe logging
 */
export function redactTelegramUpdate(update: any): string {
  if (!update) return "Empty update";

  try {
    const safeUpdate: any = {
      update_id: update.update_id || "unknown",
      timestamp: new Date().toISOString(),
    };

    // Message metadata (safe to log)
    if (update.message) {
      safeUpdate.message = {
        message_id: update.message.message_id,
        date: update.message.date,
        type: getMessageType(update.message),
        has_text: !!update.message.text,
        has_photo: !!(update.message.photo && update.message.photo.length > 0),
        has_document: !!update.message.document,
        has_caption: !!update.message.caption,
        // Redacted user info
        from: {
          is_bot: update.message.from?.is_bot,
          id_hash: update.message.from?.id
            ? hashUserId(update.message.from.id)
            : "unknown",
        },
        // Redacted chat info
        chat: {
          type: update.message.chat?.type || "unknown",
          id_hash: update.message.chat?.id
            ? hashUserId(update.message.chat.id)
            : "unknown",
        },
      };
    }

    // Callback query metadata (safe to log)
    if (update.callback_query) {
      safeUpdate.callback_query = {
        has_data: !!update.callback_query.data,
        data_length: update.callback_query.data?.length || 0,
        from: {
          id_hash: update.callback_query.from?.id
            ? hashUserId(update.callback_query.from.id)
            : "unknown",
        },
      };
    }

    return JSON.stringify(safeUpdate, null, 2);
  } catch (error) {
    return `Failed to redact update: ${error instanceof Error ? error.message : "unknown error"}`;
  }
}

/**
 * Creates a safe hash of user ID for logging purposes
 */
function hashUserId(id: number): string {
  // Simple hash for logging - not cryptographically secure but sufficient for debugging
  const hash = Math.abs(
    id
      .toString()
      .split("")
      .reduce((a, b) => {
        a = (a << 5) - a + b.charCodeAt(0);
        return a & a;
      }, 0)
  );
  return `user_${hash.toString(16).substring(0, 6)}`;
}

/**
 * Determines the type of message for logging
 */
function getMessageType(message: any): string {
  if (message.text?.startsWith("/")) return "command";
  if (message.text) return "text";
  if (message.photo) return "photo";
  if (message.document) return "document";
  return "other";
}

/**
 * Logs webhook updates safely, respecting environment settings
 */
export function logWebhookUpdate(update: any, context?: string): void {
  const isDevelopment = process.env.NODE_ENV === "development";
  const enableVerboseLogging = process.env.TELEGRAM_VERBOSE_LOGGING === "true";

  if (isDevelopment || enableVerboseLogging) {
    console.log(
      `[Webhook${context ? ` ${context}` : ""}] Telegram update:`,
      redactTelegramUpdate(update)
    );
  } else {
    // Production: minimal, safe logging
    const updateId = update?.update_id || "unknown";
    const messageType = update?.message
      ? getMessageType(update.message)
      : update?.callback_query
        ? "callback_query"
        : "unknown";
    console.log(
      `[Webhook${context ? ` ${context}` : ""}] Processing update ${updateId} (${messageType})`
    );
  }
}

/**
 * Logs webhook errors safely without exposing sensitive information
 */
export function logWebhookError(error: any, context?: string): void {
  const errorMessage = error instanceof Error ? error.message : "Unknown error";
  const errorStack = error instanceof Error ? error.stack : undefined;

  console.error(
    `[Webhook${context ? ` ${context}` : ""}] Error: ${errorMessage}`
  );

  // Only log stack trace in development
  if (process.env.NODE_ENV === "development" && errorStack) {
    console.error(
      `[Webhook${context ? ` ${context}` : ""}] Stack:`,
      errorStack
    );
  }
}
