import sharp from "sharp";

export interface ImageCompressionOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: "jpeg" | "png" | "webp";
}

export async function compressImage(
  imageBuffer: Buffer,
  options: ImageCompressionOptions = {}
): Promise<Buffer> {
  const {
    maxWidth = 1024,
    maxHeight = 1024,
    quality = 80,
    format = "jpeg",
  } = options;

  try {
    let sharpInstance = sharp(imageBuffer).resize(maxWidth, maxHeight, {
      fit: "inside",
      withoutEnlargement: true,
    });

    switch (format) {
      case "jpeg":
        sharpInstance = sharpInstance.jpeg({ quality });
        break;
      case "png":
        sharpInstance = sharpInstance.png({ quality });
        break;
      case "webp":
        sharpInstance = sharpInstance.webp({ quality });
        break;
      default:
        sharpInstance = sharpInstance.jpeg({ quality });
    }

    return await sharpInstance.toBuffer();
  } catch (error) {
    console.error("Error compressing image:", error);
    throw new Error("Failed to compress image");
  }
}

export async function getImageMetadata(imageBuffer: Buffer) {
  try {
    const metadata = await sharp(imageBuffer).metadata();
    return {
      width: metadata.width,
      height: metadata.height,
      format: metadata.format,
      size: metadata.size,
    };
  } catch (error) {
    console.error("Error getting image metadata:", error);
    throw new Error("Failed to get image metadata");
  }
}

export function calculateCompressionRatio(
  originalSize: number,
  compressedSize: number
): number {
  return Math.round((1 - compressedSize / originalSize) * 100);
}
