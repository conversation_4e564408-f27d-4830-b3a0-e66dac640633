/**
 * Validation and formatting utilities for contact data
 */

export interface ValidationResult {
  isValid: boolean;
  formatted?: string;
  error?: string;
}

/**
 * Validate and normalize email addresses
 */
export function validateEmail(email: string): ValidationResult {
  if (!email || typeof email !== 'string') {
    return { isValid: false, error: 'Email is required' };
  }

  const trimmed = email.trim().toLowerCase();
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  if (!emailRegex.test(trimmed)) {
    return { isValid: false, error: 'Invalid email format' };
  }

  return { isValid: true, formatted: trimmed };
}

/**
 * Format phone numbers to international standard
 */
export function formatPhoneNumber(phone: string, defaultCountryCode = '+1'): ValidationResult {
  if (!phone || typeof phone !== 'string') {
    return { isValid: false, error: 'Phone number is required' };
  }

  // Remove all non-digit characters except spaces between area and local number
  const digits = phone.replace(/\D/g, '');

  if (digits.length === 0) {
    return { isValid: false, error: 'No digits found in phone number' };
  }

  // Handle different phone number lengths
  let formatted: string;

  if (digits.length === 10) {
    // US/Canada format: (XXX) XXX-XXXX
    formatted = `${defaultCountryCode} (${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
  } else if (digits.length === 11 && digits.startsWith('1')) {
    // US/Canada with country code: +1 (XXX) XXX-XXXX
    formatted = `+1 (${digits.slice(1, 4)}) ${digits.slice(4, 7)}-${digits.slice(7)}`;
  } else if (digits.length === 12 && digits.startsWith('1')) {
    // Handle case where there's an extra digit (like 155571821921 -> +****************)
    // Assume the extra digit is in the wrong place and take first 11 digits
    const correctedDigits = digits.slice(0, 11);
    formatted = `+1 (${correctedDigits.slice(1, 4)}) ${correctedDigits.slice(4, 7)}-${correctedDigits.slice(7)}`;
  } else if (digits.length >= 7) {
    // International format: + country code + number
    const hasCountryCode = phone.includes('+') || digits.length > 10;
    if (hasCountryCode) {
      formatted = `+${digits}`;
    } else {
      formatted = `${defaultCountryCode} ${digits}`;
    }
  } else {
    return { isValid: false, error: 'Phone number too short' };
  }

  return { isValid: true, formatted };
}

/**
 * Validate and normalize URLs
 */
export function validateUrl(url: string): ValidationResult {
  if (!url || typeof url !== 'string') {
    return { isValid: false, error: 'URL is required' };
  }

  let trimmed = url.trim();

  // Add protocol if missing
  if (!trimmed.startsWith('http://') && !trimmed.startsWith('https://')) {
    trimmed = `https://${trimmed}`;
  }

  try {
    const urlObj = new URL(trimmed);
    return { isValid: true, formatted: urlObj.toString() };
  } catch {
    return { isValid: false, error: 'Invalid URL format' };
  }
}

/**
 * Extract and validate LinkedIn URL from text
 */
export function extractLinkedInUrl(text: string): string | null {
  const linkedinRegex = /(?:https?:\/\/)?(?:www\.)?linkedin\.com\/in\/[\w-]+\/?/i;
  const match = text.match(linkedinRegex);

  if (match) {
    const result = validateUrl(match[0]);
    return result.isValid ? result.formatted! : null;
  }

  return null;
}

/**
 * Extract social media handles from text
 */
export function extractSocialHandles(text: string) {
  const handles = {
    telegram: null as string | null,
    twitter: null as string | null,
    instagram: null as string | null,
    whatsapp: null as string | null,
  };

  // Telegram: @username or t.me/username
  const telegramMatch = text.match(/@(\w+)|t\.me\/(\w+)/i);
  if (telegramMatch) {
    handles.telegram = `@${telegramMatch[1] || telegramMatch[2]}`;
  }

  // Twitter: @username or twitter.com/username or x.com/username
  const twitterMatch = text.match(/@(\w+)|(?:twitter|x)\.com\/(\w+)/i);
  if (twitterMatch) {
    handles.twitter = `@${twitterMatch[1] || twitterMatch[2]}`;
  }

  // Instagram: @username or instagram.com/username
  const instagramMatch = text.match(/@(\w+)|instagram\.com\/(\w+)/i);
  if (instagramMatch) {
    handles.instagram = `@${instagramMatch[1] || instagramMatch[2]}`;
  }

  // WhatsApp: Extract phone numbers that might be WhatsApp
  const whatsappMatch = text.match(/whatsapp|wa\.me\/(\+?\d+)/i);
  if (whatsappMatch && whatsappMatch[1]) {
    const phoneResult = formatPhoneNumber(whatsappMatch[1]);
    if (phoneResult.isValid) {
      handles.whatsapp = phoneResult.formatted!;
    }
  }

  return handles;
}

/**
 * Calculate lead score based on data completeness
 */
export function calculateLeadScore(data: Record<string, any>): number {
  const weights = {
    firstName: 10,
    lastName: 10,
    emailWork: 20,
    phoneMobile: 15,
    companyName: 15,
    jobTitle: 10,
    linkedinUrl: 10,
    addressCity: 5,
    companyWebsite: 5,
  };

  let score = 0;
  let maxScore = 0;

  for (const [field, weight] of Object.entries(weights)) {
    maxScore += weight;
    if (data[field] && data[field].trim && data[field].trim() !== '') {
      score += weight;
    }
  }

  return Math.round((score / maxScore) * 100);
}

/**
 * Determine data quality level based on completeness and validation
 */
export function getDataQuality(data: Record<string, any>): 'high' | 'medium' | 'low' {
  const score = calculateLeadScore(data);

  if (score >= 80) return 'high';
  if (score >= 50) return 'medium';
  return 'low';
}

/**
 * Clean and normalize company names
 */
export function normalizeCompanyName(company: string): string {
  if (!company || typeof company !== 'string') return '';

  return company
    .trim()
    .replace(/\b(inc|llc|ltd|corp|corporation|company|co)\b\.?/gi, '')
    .replace(/\s+/g, ' ')
    .trim();
}