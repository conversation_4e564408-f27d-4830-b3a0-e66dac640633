import { OCR_MODEL, ANALYSIS_MODEL, FALLBACK_MODEL } from "./config";
import {
  validateEmail,
  formatPhoneNumber,
  validateUrl,
  calculateLeadScore,
  getDataQuality,
  normalizeCompanyName,
} from "./validation";

// Legacy interface for backward compatibility
export interface ContactData {
  leadName?: string;
  leadWebsite?: string;
  leadPhone?: string;
  leadTelegram?: string;
  leadGeneralInfo?: string;
}

// Comprehensive contact data interface
export interface DetailedContactData {
  // Professional Information
  firstName?: string;
  lastName?: string;
  middleName?: string;
  jobTitle?: string;
  department?: string;
  companyName?: string;
  companyWebsite?: string;
  industry?: string;

  // Contact Details
  emailWork?: string;
  emailPersonal?: string;
  phoneMobile?: string;
  phoneOffice?: string;
  phoneFax?: string;
  linkedinUrl?: string;
  telegramHandle?: string;
  whatsappNumber?: string;
  twitterHandle?: string;
  instagramHandle?: string;

  // Location
  addressStreet?: string;
  addressCity?: string;
  addressState?: string;
  addressZip?: string;
  addressCountry?: string;

  // Metadata
  leadSource?: string;
  leadScore?: number;
  dataQuality?: 'high' | 'medium' | 'low';
  tags?: string[];
  notes?: string;

  // Legacy fields (aggregated from detailed fields)
  leadName?: string;
  leadWebsite?: string;
  leadPhone?: string;
  leadTelegram?: string;
  leadGeneralInfo?: string;
}

export interface SessionData {
  type: "text" | "photo" | "document";
  content: string;
  timestamp: number;
}

export async function extractContactFromImage(
  imageBase64: string,
  mimeType: string
): Promise<string> {
  // Robust logging for debugging image-processing issues across providers
  try {
    console.log("[extractContactFromImage] start", {
      mimeType,
      base64Length: imageBase64?.length ?? 0,
    });
  } catch {}

  // Send multimodal request directly to OpenRouter Chat Completions using image_url with data URL
  const dataUrl = `data:${mimeType};base64,${imageBase64}`;
  const messages = [
    {
      role: "user" as const,
      content: [
        { type: "text" as const, text: "Extract all visible text from this image. Reply with the raw text only." },
        { type: "image_url" as const, image_url: { url: dataUrl } },
      ],
    },
  ];

  async function call(model: string) {
    const url = "https://openrouter.ai/api/v1/chat/completions";
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${process.env.OPENROUTER_API_KEY ?? ""}`,
      "HTTP-Referer": "https://buddycontact.local/ocr",
      "X-Title": "BuddyContact OCR",
    };
    const body = JSON.stringify({ model, messages });
    console.log("[extractContactFromImage] HTTP request", { url, model, bodyLen: body.length });
    const res = await fetch(url, { method: "POST", headers, body, signal: AbortSignal.timeout(5000) });
    const raw = await res.text();
    console.log("[extractContactFromImage] HTTP response", { status: res.status, statusText: res.statusText, rawLen: raw.length });
    try {
      const json = JSON.parse(raw);
      console.log("[extractContactFromImage] HTTP json (truncated)", JSON.stringify(json).slice(0, 1200));
      if (!res.ok) throw new Error(json?.error?.message || `HTTP ${res.status}`);
      const content = json?.choices?.[0]?.message?.content as string | undefined;
      if (!content) throw new Error("No content in response");
      return content;
    } catch (e) {
      console.log("[extractContactFromImage] parse error or bad response", { message: (e as any)?.message });
      throw e;
    }
  }

  console.log('[extractContactFromImage] using models', { ocr: OCR_MODEL, fallback: FALLBACK_MODEL });
  try {
    return await call(OCR_MODEL);
  } catch (e) {
    console.log('[extractContactFromImage] OCR failed, trying fallback', { message: (e as any)?.message });
    return await call(FALLBACK_MODEL);
  }
}

export async function organizeContactData(
  sessionData: SessionData[],
  salesRepName: string
): Promise<DetailedContactData & { formattedPreview: string }> {
  const allContent = sessionData
    .map((item) => `[${item.type.toUpperCase()}] ${item.content}`)
    .join("\n\n");

  const organizationPrompt = {
    role: "user" as const,
    content: `Extract contact information from this data and return a JSON object:

DATA:
${allContent}

Extract: names, job title, company, emails, phones, social media, address. Split full names into firstName/lastName. Classify phones (mobile/office) and emails (work/personal).

Return JSON with these exact fields (use null for missing data):
{
  "firstName": "first name or null",
  "lastName": "last name or null",
  "middleName": "middle name or null",
  "jobTitle": "job title or null",
  "department": "department or null",
  "companyName": "company name or null",
  "companyWebsite": "website or null",
  "industry": "industry or null",
  "emailWork": "work email or null",
  "emailPersonal": "personal email or null",
  "phoneMobile": "mobile phone or null",
  "phoneOffice": "office phone or null",
  "phoneFax": "fax or null",
  "linkedinUrl": "LinkedIn URL or null",
  "telegramHandle": "Telegram handle or null",
  "whatsappNumber": "WhatsApp or null",
  "twitterHandle": "Twitter handle or null",
  "instagramHandle": "Instagram handle or null",
  "addressStreet": "street or null",
  "addressCity": "city or null",
  "addressState": "state or null",
  "addressZip": "zip or null",
  "addressCountry": "country or null",
  "leadSource": "source or null",
  "tags": ["relevant", "keywords"] or null,
  "notes": "key details or null",
  "formattedPreview": "📋 **Contact Preview** ⭐⭐⭐\\n**Sales Person:** ${salesRepName}\\n**Contact:** [firstName lastName] - [jobTitle]\\n**Company:** [companyName] - [industry]\\n**Email:** [primaryEmail]\\n**Phone:** [primaryPhone]\\n**Location:** [city, state]\\n**LinkedIn:** [linkedinUrl or 'Not provided']\\n**Notes:** [key highlights]"
}

Return ONLY the JSON object, no markdown or code blocks.`,
  };

  async function callText(model: string) {
    const url = "https://openrouter.ai/api/v1/chat/completions";
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${process.env.OPENROUTER_API_KEY ?? ""}`,
      "HTTP-Referer": "https://buddycontact.local/organize",
      "X-Title": "BuddyContact Organizer",
    };
    const messages = [ { role: "user" as const, content: String(organizationPrompt.content) } ];
    const body = JSON.stringify({ model, messages });
    console.log("[organizeContactData] HTTP request", { url, model, bodyLen: body.length });
    const startTime = Date.now();
    const res = await fetch(url, { method: "POST", headers, body, signal: AbortSignal.timeout(7000) });
    const raw = await res.text();
    const duration = Date.now() - startTime;
    console.log("[organizeContactData] HTTP response", { status: res.status, statusText: res.statusText, rawLen: raw.length, duration: `${duration}ms` });
    let json: any;
    try {
      json = JSON.parse(raw);
      console.log("[organizeContactData] HTTP json (truncated)", JSON.stringify(json).slice(0, 1200));
    } catch (e) {
      console.error("[organizeContactData] parse error", e);
      throw new Error("Organizer parse error");
    }
    if (!res.ok) throw new Error(json?.error?.message || `HTTP ${res.status}`);
    const content = json?.choices?.[0]?.message?.content as string | undefined;
    if (!content) throw new Error("No content in response");
    return content;
  }

  try {
    const text = await callText(ANALYSIS_MODEL);
    try {
      const cleanedText = cleanJsonResponse(text);
      const parsed = JSON.parse(cleanedText);
      return processAndValidateExtractedData(parsed, salesRepName);
    } catch (parseError) {
      console.error("[organizeContactData] Failed to parse AI response as JSON", parseError);
      return createFallbackResponse(text, salesRepName);
    }
  } catch (error) {
    console.error("[organizeContactData] analysis model failed", error);
    try {
      const text = await callText(FALLBACK_MODEL);
      try {
        const cleanedText = cleanJsonResponse(text);
        const parsed = JSON.parse(cleanedText);
        return processAndValidateExtractedData(parsed, salesRepName);
      } catch (parseError) {
        console.error("[organizeContactData] Failed to parse fallback AI response as JSON", parseError);
        return createFallbackResponse(text, salesRepName);
      }
    } catch (fallbackError) {
      console.error("[organizeContactData] fallback model failed", fallbackError);
      throw new Error("Failed to organize contact data with both models");
    }
  }
}

/**
 * Combined OCR + Grok processing for images to stay within Vercel timeout limits
 */
export async function extractAndOrganizeFromImage(
  imageBase64: string,
  mimeType: string,
  salesRepName: string
): Promise<{
  ocrText: string;
  organizedData: DetailedContactData & { formattedPreview: string };
}> {
  console.log("[extractAndOrganizeFromImage] start", {
    mimeType,
    base64Length: imageBase64?.length ?? 0,
    salesRepName,
  });

  try {
    // Step 1: Extract text from image (5s timeout)
    const ocrText = await extractContactFromImage(imageBase64, mimeType);

    // Step 2: Immediately organize the extracted text (4s timeout)
    const sessionData = [{
      type: "photo" as const,
      content: ocrText,
      timestamp: Date.now(),
    }];

    const organizedData = await organizeContactData(sessionData, salesRepName);

    console.log("[extractAndOrganizeFromImage] success", {
      ocrTextLength: ocrText.length,
      organizedDataReady: !!organizedData,
    });

    return {
      ocrText,
      organizedData,
    };
  } catch (error) {
    console.error("[extractAndOrganizeFromImage] failed", error);
    throw error;
  }
}

/**
 * Clean JSON response from AI (remove markdown code blocks)
 */
function cleanJsonResponse(text: string): string {
  // Remove markdown code blocks and extra whitespace
  return text
    .replace(/```json\s*/gi, '')
    .replace(/```\s*$/g, '')
    .trim();
}

/**
 * Process and validate extracted data, applying formatting and validation
 */
function processAndValidateExtractedData(
  parsed: any,
  salesRepName: string
): DetailedContactData & { formattedPreview: string } {
  const data: DetailedContactData = {};

  // Basic info
  data.firstName = parsed.firstName || undefined;
  data.lastName = parsed.lastName || undefined;
  data.middleName = parsed.middleName || undefined;
  data.jobTitle = parsed.jobTitle || undefined;
  data.department = parsed.department || undefined;
  data.industry = parsed.industry || undefined;

  // Company info
  if (parsed.companyName) {
    data.companyName = normalizeCompanyName(parsed.companyName);
  }

  if (parsed.companyWebsite) {
    const websiteResult = validateUrl(parsed.companyWebsite);
    if (websiteResult.isValid) {
      data.companyWebsite = websiteResult.formatted;
    }
  }

  // Email validation
  if (parsed.emailWork) {
    const emailResult = validateEmail(parsed.emailWork);
    if (emailResult.isValid) {
      data.emailWork = emailResult.formatted;
    }
  }

  if (parsed.emailPersonal) {
    const emailResult = validateEmail(parsed.emailPersonal);
    if (emailResult.isValid) {
      data.emailPersonal = emailResult.formatted;
    }
  }

  // Phone validation and formatting
  if (parsed.phoneMobile) {
    const phoneResult = formatPhoneNumber(parsed.phoneMobile);
    if (phoneResult.isValid) {
      data.phoneMobile = phoneResult.formatted;
    }
  }

  if (parsed.phoneOffice) {
    const phoneResult = formatPhoneNumber(parsed.phoneOffice);
    if (phoneResult.isValid) {
      data.phoneOffice = phoneResult.formatted;
    }
  }

  if (parsed.phoneFax) {
    const phoneResult = formatPhoneNumber(parsed.phoneFax);
    if (phoneResult.isValid) {
      data.phoneFax = phoneResult.formatted;
    }
  }

  // Social media
  if (parsed.linkedinUrl) {
    const urlResult = validateUrl(parsed.linkedinUrl);
    if (urlResult.isValid) {
      data.linkedinUrl = urlResult.formatted;
    }
  }

  data.telegramHandle = parsed.telegramHandle || undefined;
  data.whatsappNumber = parsed.whatsappNumber || undefined;
  data.twitterHandle = parsed.twitterHandle || undefined;
  data.instagramHandle = parsed.instagramHandle || undefined;

  // Address
  data.addressStreet = parsed.addressStreet || undefined;
  data.addressCity = parsed.addressCity || undefined;
  data.addressState = parsed.addressState || undefined;
  data.addressZip = parsed.addressZip || undefined;
  data.addressCountry = parsed.addressCountry || undefined;

  // Metadata
  data.leadSource = parsed.leadSource || undefined;

  // Normalize tags from AI response (can be array or comma-separated string)
  if (parsed.tags) {
    let tagArray: string[] = [];

    if (Array.isArray(parsed.tags)) {
      // If it's already an array, map to strings and trim
      tagArray = parsed.tags.map((tag: any) => String(tag).trim()).filter((tag: string) => tag !== '');
    } else if (typeof parsed.tags === 'string') {
      // If it's a string, split on commas, semicolons, or newlines
      tagArray = parsed.tags
        .split(/[,;\n]+/)
        .map((tag: string) => tag.trim())
        .filter((tag: string) => tag !== '');
    }

    // Deduplicate the array
    const uniqueTags = [...new Set(tagArray)];

    // Set to undefined if empty, otherwise use the cleaned array
    data.tags = uniqueTags.length > 0 ? uniqueTags : undefined;
  } else {
    data.tags = undefined;
  }

  data.notes = parsed.notes || undefined;

  // Calculate metadata
  data.leadScore = calculateLeadScore(data);
  data.dataQuality = getDataQuality(data);

  // Create legacy fields for backward compatibility with fallbacks
  data.leadName = data.firstName && data.lastName
    ? `${data.firstName} ${data.lastName}`.trim()
    : data.firstName || data.lastName || (parsed as any).leadName || undefined;

  data.leadWebsite = data.companyWebsite || (parsed as any).leadWebsite || undefined;
  data.leadPhone = data.phoneMobile || data.phoneOffice || (parsed as any).leadPhone || undefined;
  data.leadTelegram = data.telegramHandle || (parsed as any).leadTelegram || undefined;

  // Aggregate general info
  const generalInfoParts = [];
  if (data.companyName) generalInfoParts.push(`Company: ${data.companyName}`);
  if (data.jobTitle) generalInfoParts.push(`Role: ${data.jobTitle}`);
  if (data.department) generalInfoParts.push(`Department: ${data.department}`);
  if (data.industry) generalInfoParts.push(`Industry: ${data.industry}`);
  if (data.addressCity && data.addressState) {
    generalInfoParts.push(`Location: ${data.addressCity}, ${data.addressState}`);
  }
  if (data.notes) generalInfoParts.push(`Notes: ${data.notes}`);

  data.leadGeneralInfo = generalInfoParts.length > 0
    ? generalInfoParts.join(' | ')
    : undefined;

  // Create formatted preview
  const qualityStars = data.dataQuality === 'high' ? '⭐⭐⭐'
    : data.dataQuality === 'medium' ? '⭐⭐' : '⭐';

  const formattedPreview = parsed.formattedPreview || createFormattedPreview(data, salesRepName, qualityStars);

  return {
    ...data,
    formattedPreview,
  };
}

/**
 * Create fallback response when AI parsing fails
 */
function createFallbackResponse(
  text: string,
  salesRepName: string
): DetailedContactData & { formattedPreview: string } {
  return {
    leadGeneralInfo: text,
    notes: text.slice(0, 500),
    leadScore: 10,
    dataQuality: 'low',
    formattedPreview: `📋 **Contact Preview** ⭐

**Sales Person:** ${salesRepName}
**Contact:** Not provided
**Company:** Not provided
**Email:** Not provided
**Phone:** Not provided
**Location:** Not provided
**LinkedIn:** Not provided
**Notes:** ${text.slice(0, 200)}${text.length > 200 ? "..." : ""}`,
  };
}

/**
 * Create formatted preview from extracted data
 */
function createFormattedPreview(
  data: DetailedContactData,
  salesRepName: string,
  qualityStars: string
): string {
  const contact = data.leadName || "Not provided";
  const jobTitle = data.jobTitle ? ` - ${data.jobTitle}` : "";
  const company = data.companyName || "Not provided";
  const industry = data.industry ? ` - ${data.industry}` : "";
  const email = data.emailWork || data.emailPersonal || "Not provided";
  const phone = data.phoneMobile || data.phoneOffice || "Not provided";
  const location = data.addressCity && data.addressState
    ? `${data.addressCity}, ${data.addressState}`
    : "Not provided";
  const linkedin = data.linkedinUrl || "Not provided";
  const notes = data.notes || "No additional notes";

  return `📋 **Contact Preview** ${qualityStars}

**Sales Person:** ${salesRepName}
**Contact:** ${contact}${jobTitle}
**Company:** ${company}${industry}
**Email:** ${email}
**Phone:** ${phone}
**Location:** ${location}
**LinkedIn:** ${linkedin}
**Notes:** ${notes}`;
}
