import { createOpenAI } from "@ai-sdk/openai";

export const openrouter = createOpenAI({
  apiKey: process.env.OPENROUTER_API_KEY,
  baseURL: "https://openrouter.ai/api/v1",
});

// Use Gemini for fast OCR processing
export const OCR_MODEL = "google/gemini-2.5-flash";
// Use Grok 4 Fast for advanced text transformation and data organization
export const ANALYSIS_MODEL = process.env.OPENROUTER_PRIMARY_MODEL || "x-ai/grok-4-fast";
// Keep Gemini as fallback for reliability
export const FALLBACK_MODEL = "google/gemini-2.5-flash";

export function getOCRModel() {
  return openrouter(OCR_MODEL);
}

export function getAnalysisModel() {
  return openrouter(ANALYSIS_MODEL);
}

export function getFallbackModel() {
  return openrouter(FALLBACK_MODEL);
}
