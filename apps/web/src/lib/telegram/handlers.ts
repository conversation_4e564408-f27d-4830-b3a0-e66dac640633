import { api } from "@BuddyContact/backend/convex/_generated/api";
import { ConvexHttpClient } from "convex/browser";
import type { Id } from "@BuddyContact/backend/convex/_generated/dataModel";
import { organizeContactData, extractAndOrganizeFromImage } from "../ai/extract";
import { compressImage } from "../utils/image-compress";
import type { BotContext } from "./bot";
import { getBot } from "./bot";

let convex: ConvexHttpClient | null = null;

function getConvex() {
  if (!convex) {
    const url =
      process.env.NEXT_PUBLIC_CONVEX_URL ||
      "https://dummy-url-for-build.convex.cloud";
    convex = new ConvexHttpClient(url);
  }
  return convex;
}

const activeTimeouts = new Map<string, NodeJS.Timeout>();

// Temporary storage for organized contact data (cleared after use)
const organizedContactData = new Map<string, any>();

// Track saved contacts per session
const sessionContacts = new Map<string, string[]>();

// Track processed data cursors per session to prevent duplicate processing
const sessionProcessedCursors = new Map<string, number>();

// Track processed image file IDs to prevent duplicate image processing
const processedImageIds = new Map<string, Set<string>>();

// Rate limiting for auto-save function
const autoSaveLastRun = new Map<string, number>();
const AUTO_SAVE_COOLDOWN = 5000; // 5 seconds

// Helper function to check if we have minimum viable contact data (only checks unprocessed data)
function hasMinimumContactData(sessionData: any[], processedCursor: number = 0): boolean {
  const unprocessedData = sessionData.slice(processedCursor);
  if (unprocessedData.length === 0) return false;

  const allContent = unprocessedData.map(item => item.content).join(' ').toLowerCase();

  // Must have at least a name AND (email OR phone)
  const hasName = /[a-z]{2,}\s+[a-z]{2,}/i.test(allContent); // First Last name pattern
  const hasEmail = /@[a-z0-9.-]+\.[a-z]{2,}/i.test(allContent);
  const hasPhone = /[\d\s\-\(\)\+]{10,}/i.test(allContent);

  return hasName && (hasEmail || hasPhone);
}

// Helper function to detect contact separator
function isContactSeparator(content: string): boolean {
  const separators = ['next', 'new', 'another', '---', 'next contact', 'new contact'];
  const cleanContent = content.toLowerCase().trim();
  return separators.some(sep => cleanContent === sep || cleanContent.includes(sep));
}

// Auto-save contact when we have enough data
async function autoSaveContact(
  ctx: BotContext,
  activeSession: any,
  salesRepId: Id<"salesReps">,
  salesRepName: string
): Promise<boolean> {
  try {
    const sessionKey = `${ctx.from?.id}_${activeSession._id}`;

    // Rate limiting - prevent auto-save from running too frequently
    const lastRun = autoSaveLastRun.get(sessionKey) || 0;
    const now = Date.now();
    if (now - lastRun < AUTO_SAVE_COOLDOWN) {
      console.log(`Auto-save rate limited for session ${sessionKey}`);
      return false;
    }

    const processedCursor = sessionProcessedCursors.get(sessionKey) || 0;

    // Check if we have minimum data to save (only unprocessed data)
    if (!hasMinimumContactData(activeSession.sessionData, processedCursor)) {
      return false;
    }

    // Only process unprocessed data
    const unprocessedData = activeSession.sessionData.slice(processedCursor);

    // Organize the contact data from unprocessed items only
    const organizedData = await organizeContactData(
      unprocessedData.map((item: any) => ({
        type: item.type,
        content: item.content,
        timestamp: item.timestamp,
      })),
      salesRepName
    );

    // Improved duplicate prevention - allow multiple different contacts
    const existingContacts = sessionContacts.get(sessionKey) || [];

    // Only block if we have too many contacts (>3) to prevent spam, but allow legitimate multiple contacts
    if (existingContacts.length > 3) {
      console.log(`Too many contacts in session (${existingContacts.length}), skipping to prevent spam`);
      return false;
    }

    // Save to database - only use fields that match current validator
    const contactId = await getConvex().mutation(api.contacts.createContact, {
      salesRepId,

      // Legacy fields (only these are in current validator)
      leadName: organizedData.leadName,
      leadWebsite: organizedData.leadWebsite,
      leadPhone: organizedData.leadPhone,
      leadTelegram: organizedData.leadTelegram,
      leadGeneralInfo: organizedData.leadGeneralInfo,

      // System fields
      rawData: JSON.stringify(unprocessedData),
      sessionId: activeSession._id,
    });

    // Track saved contact
    const savedContacts = sessionContacts.get(sessionKey) || [];
    savedContacts.push(contactId);
    sessionContacts.set(sessionKey, savedContacts);

    // Update processed cursor to mark all current data as processed
    sessionProcessedCursors.set(sessionKey, activeSession.sessionData.length);

    // Update rate limiting timestamp
    autoSaveLastRun.set(sessionKey, now);

    // Notify user
    const contactName = organizedData.leadName || 'Contact';
    const company = organizedData.companyName ? ` (${organizedData.companyName})` : '';
    const contactCount = savedContacts.length;

    await ctx.reply(`✅ Contact saved: **${contactName}${company}**

📊 Total saved this session: ${contactCount}
📝 Send another contact or wait 30s to finish.`);

    return true;
  } catch (error) {
    console.error('Auto-save failed:', error);
    return false;
  }
}

export function setupBotHandlers() {
  const bot = getBot();

  bot.command("start", async (ctx: BotContext) => {
    await ctx.reply(`👋 Welcome to BuddyContact!

I'm here to help you collect and organize contact information from leads.

Available commands:
/contact - Start collecting contact information
/info - Learn how the bot works
/help - Show this help message

Send me names, phone numbers, business cards, QR codes, or any other contact information, and I'll organize it all for you!`);
  });

  bot.command("help", async (ctx: BotContext) => {
    await ctx.reply(`🆘 BuddyContact Help

Commands:
• /contact - Start a new contact collection session
• /cancel or /close - Cancel current session
• /info - Learn how the bot works
• /help - Show this help message

How to use:
1. Type /contact to start
2. Send me any combination of:
   - Text with contact details
   - Photos of business cards
   - Screenshots with contact info
   - QR codes
   - Documents with contact information

3. After 3 seconds of inactivity, I'll ask if you're done
4. I'll organize everything and show you a preview
5. Confirm to save the contact!

💡 Tips:
• I'll show you what text I extract from each image
• You can cancel anytime with /cancel or /close
• Mix and match text, photos, and documents freely

That's it! I'll handle all the organization and formatting for you.`);
  });

  bot.command("info", async (ctx: BotContext) => {
    await ctx.reply(`🤖 BuddyContact - AI-Powered Contact Management

🎯 **What I Do:**
I help sales representatives collect, organize, and manage contact information from leads using advanced AI technology.

🔧 **How I Work:**

**1. Smart Input Processing**
• Text messages with contact details
• Business card photos (OCR extraction)
• Screenshots with contact information
• QR codes and digital business cards
• Documents with contact data

**2. AI-Powered Organization**
• Extract names, phone numbers, emails, websites
• Identify company information and job titles
• Organize scattered information into structured format
• Handle multiple languages and formats

**3. Auto-Processing**
• Contacts saved automatically when complete
• 30-second session timeout for inactivity
• Support for multiple contacts per session
• Type "next" or "---" to separate contacts

**4. Data Output Format:**
📋 **Contact Preview:**
• Sales Person: [Your name]
• Lead Name: [Extracted name]
• Lead Website: [Company website]
• Lead General Info: [Role, company, notes]
• Phone: [Formatted number]
• Telegram: [Handle if provided]

**5. Export & Storage**
• All contacts saved to secure database
• Excel export functionality available
• Complete audit trail of all interactions
• Associate contacts with specific sales reps

🚀 **Powered By:**
• OpenRouter + Gemini 2.0 Flash AI
• Advanced image compression
• Real-time data processing
• Enterprise-grade security

Ready to collect some contacts? Use /contact to get started!`);
  });

  // Cancel/Close session command handler (shared logic)
  const handleSessionCancel = async (ctx: BotContext) => {
    const telegramUserId = ctx.from?.id.toString();
    if (!telegramUserId) return;

    const activeSession = await getConvex().query(
      api.sessions.getActiveSession,
      {
        telegramUserId,
      }
    );

    if (activeSession) {
      await getConvex().mutation(api.sessions.closeSession, {
        sessionId: activeSession._id,
      });

      // Clear any pending timeout
      if (activeTimeouts.has(telegramUserId)) {
        clearTimeout(activeTimeouts.get(telegramUserId)!);
        activeTimeouts.delete(telegramUserId);
      }

      await ctx.reply(
        "✅ Contact session cancelled successfully.\n\nUse /contact to start a new session."
      );
    } else {
      await ctx.reply(
        "❌ No active session to cancel.\n\nUse /contact to start a new session."
      );
    }
  };

  bot.command("cancel", handleSessionCancel);
  bot.command("close", handleSessionCancel);

  bot.command("contact", async (ctx: BotContext) => {
    // Ignore commands from the bot itself
    if (ctx.from?.is_bot) return;

    const telegramUserId = ctx.from?.id.toString();
    if (!(telegramUserId && ctx.salesRepId)) return;

    const existingSession = await getConvex().query(
      api.sessions.getActiveSession,
      {
        telegramUserId,
      }
    );

    if (existingSession) {
      await ctx.reply(
        "⚠️ You already have an active contact session. Please finish or cancel the current one first."
      );
      return;
    }

    const sessionId = await getConvex().mutation(api.sessions.createSession, {
      telegramUserId,
      salesRepId: ctx.salesRepId!,
    });

    await ctx.reply(`🔄 Contact collection started!

Send me any contact information:
• Name, phone, email, website
• Photos of business cards
• Screenshots or documents
• QR codes

I'll organize everything for you. Send as much as you have!`);

    scheduleTimeoutCheck(telegramUserId, sessionId);
  });

  bot.on(
    ["message:text", "message:photo", "message:document"],
    async (ctx: BotContext) => {
      if (ctx.message?.text?.startsWith("/")) return;

      // Ignore messages from the bot itself to prevent feedback loops
      if (ctx.message?.from?.is_bot) return;

      const telegramUserId = ctx.from?.id.toString();
      if (!telegramUserId) return;

      const activeSession = await getConvex().query(
        api.sessions.getActiveSession,
        {
          telegramUserId,
        }
      );

      if (!activeSession) return;

      let content = "";
      const messageId = ctx.message?.message_id.toString() || "";

      if (ctx.message?.text) {
        content = ctx.message.text;

        // Check if this is a contact separator
        if (isContactSeparator(content)) {
          // Get sales rep info for auto-save
          const salesRep = await getConvex().query(
            api.salesReps.getSalesRepByTelegramId,
            { telegramId: telegramUserId }
          );

          if (salesRep) {
            const salesRepName = `${salesRep.firstName} ${salesRep.lastName || ""}`.trim();

            // Try to save current contact before starting new one
            await autoSaveContact(ctx, activeSession, activeSession.salesRepId, salesRepName);
          }

          // Don't add separator text to session data
          await ctx.reply("📝 Ready for next contact! Send new contact information.");
          scheduleTimeoutCheck(telegramUserId, activeSession._id);
          return;
        }

        // Add normal text to session
        await getConvex().action(api.sessions.addSessionData, {
          sessionId: activeSession._id,
          messageId,
          type: "text",
          content,
        });

        // Get updated session data for auto-save check
        const updatedSession = await getConvex().query(api.sessions.getActiveSession, {
          telegramUserId,
        });

        if (updatedSession) {
          // Try to auto-save if we have enough data
          const salesRep = await getConvex().query(
            api.salesReps.getSalesRepByTelegramId,
            { telegramId: telegramUserId }
          );

          if (salesRep) {
            const salesRepName = `${salesRep.firstName} ${salesRep.lastName || ""}`.trim();
            await autoSaveContact(ctx, updatedSession, activeSession.salesRepId, salesRepName);
          }
        }

        // Schedule timeout check after text message
        scheduleTimeoutCheck(ctx.from?.id.toString() || "", activeSession._id);
      } else if (ctx.message?.photo && ctx.message.photo.length > 0) {
        const largestPhoto = ctx.message.photo[ctx.message.photo.length - 1];

        // Check for duplicate image processing
        const sessionKey = `${ctx.from?.id}_${activeSession._id}`;
        const processedImages = processedImageIds.get(sessionKey) || new Set();

        if (processedImages.has(largestPhoto.file_id)) {
          console.log(`Image ${largestPhoto.file_id} already processed, skipping`);
          await ctx.reply("⚠️ This image has already been processed in this session.");
          return;
        }

        try {
          const file = await ctx.api.getFile(largestPhoto.file_id);
          const fileUrl = `https://api.telegram.org/file/bot${process.env.TELEGRAM_BOT_TOKEN}/${file.file_path}`;

          const response = await fetch(fileUrl);
          const imageBuffer = Buffer.from(await response.arrayBuffer());

          const compressedBuffer = await compressImage(imageBuffer);
          const base64Image = compressedBuffer.toString("base64");

          // Get sales rep info for combined processing
          const salesRep = await getConvex().query(
            api.salesReps.getSalesRepByTelegramId,
            { telegramId: telegramUserId }
          );

          if (!salesRep) {
            await ctx.reply("⚠️ Sales rep not found. Please contact support.");
            return;
          }

          const salesRepName = `${salesRep.firstName} ${salesRep.lastName || ""}`.trim();

          // Use combined OCR + Grok processing to stay within timeout limits
          const { ocrText, organizedData } = await extractAndOrganizeFromImage(
            base64Image,
            "image/jpeg",
            salesRepName
          );

          content = ocrText;

          // Check if session still exists before saving
          const sessionCheck = await getConvex().query(api.sessions.getActiveSession, {
            telegramUserId,
          });

          if (sessionCheck && sessionCheck._id === activeSession._id) {
            // Save OCR text to session
            await getConvex().action(api.sessions.addSessionData, {
              sessionId: activeSession._id,
              messageId,
              type: "photo",
              content: content || ctx.message.caption || "Photo processed",
              imageData: base64Image,
              mimeType: "image/jpeg",
            });

            // Mark image as processed to prevent duplicates
            processedImages.add(largestPhoto.file_id);
            processedImageIds.set(sessionKey, processedImages);

            // Immediately try to save the organized contact
            try {
              const sessionKey = `${ctx.from?.id}_${sessionCheck._id}`;
              const existingContacts = sessionContacts.get(sessionKey) || [];

              // Only block if we have too many contacts (>3) to prevent spam
              if (existingContacts.length <= 3) {
                // Save organized contact immediately
                const contactId = await getConvex().mutation(api.contacts.createContact, {
                  salesRepId: activeSession.salesRepId,

                  // Legacy fields (only these are in current validator)
                  leadName: organizedData.leadName,
                  leadWebsite: organizedData.leadWebsite,
                  leadPhone: organizedData.leadPhone,
                  leadTelegram: organizedData.leadTelegram,
                  leadGeneralInfo: organizedData.leadGeneralInfo,

                  // System fields
                  rawData: JSON.stringify([{ type: "photo", content: ocrText, timestamp: Date.now() }]),
                  sessionId: activeSession._id,
                });

                // Track saved contact
                const savedContacts = sessionContacts.get(sessionKey) || [];
                savedContacts.push(contactId);
                sessionContacts.set(sessionKey, savedContacts);

                // Update processed cursor to mark this data as processed
                sessionProcessedCursors.set(sessionKey, sessionCheck.sessionData.length + 1);

                // Show organized contact preview
                await ctx.reply(`📸 **Contact saved!**

${organizedData.formattedPreview}

📊 Total saved this session: ${savedContacts.length}
📝 Send another contact or wait to finish.`);
              } else {
                await ctx.reply("⚠️ Too many contacts in this session. Please start a new session with /contact.");
              }
            } catch (saveError) {
              console.error("Failed to save organized contact:", saveError);
              // Fallback to showing OCR text
              if (content && content.trim()) {
                const preview = content.length > 400 ? content.substring(0, 400) + "..." : content;
                await ctx.reply(`📸 Image processed! Here's what I extracted:

📝 **Extracted Text:**
${preview}

✅ This has been added to your contact session.`);
              }
            }
          } else {
            console.log("Session expired while processing image, skipping save");
            await ctx.reply("⚠️ Session expired during image processing. Use /contact to start a new session.");
            return;
          }

          // Schedule timeout check after successful processing
          scheduleTimeoutCheck(ctx.from?.id.toString() || "", activeSession._id);
        } catch (error) {
          console.error("Error processing image:", error);

          // Still try to save the image even if text extraction fails
          try {
            const file = await ctx.api.getFile(largestPhoto.file_id);
            const fileUrl = `https://api.telegram.org/file/bot${process.env.TELEGRAM_BOT_TOKEN}/${file.file_path}`;
            const response = await fetch(fileUrl);
            const imageBuffer = Buffer.from(await response.arrayBuffer());
            const compressedBuffer = await compressImage(imageBuffer);
            const base64Image = compressedBuffer.toString("base64");

            content =
              ctx.message.caption || "Photo uploaded - text extraction failed";

            // Check if session still exists before saving
            const sessionCheck = await getConvex().query(api.sessions.getActiveSession, {
              telegramUserId,
            });

            if (sessionCheck && sessionCheck._id === activeSession._id) {
              await getConvex().action(api.sessions.addSessionData, {
                sessionId: activeSession._id,
                messageId,
                type: "photo",
                content,
                imageData: base64Image,
                mimeType: "image/jpeg",
              });
            } else {
              console.log("Session expired while processing image, skipping save");
            }
          } catch (imageError) {
            console.error("Error saving image:", imageError);
            content =
              ctx.message.caption || "Photo uploaded - could not save image";

            // Check if session still exists before saving error
            const sessionCheck = await getConvex().query(api.sessions.getActiveSession, {
              telegramUserId,
            });

            if (sessionCheck && sessionCheck._id === activeSession._id) {
              await getConvex().action(api.sessions.addSessionData, {
                sessionId: activeSession._id,
                messageId,
                type: "photo",
                content,
              });
            } else {
              console.log("Session expired while processing image error, skipping save");
            }
          }

          await ctx.reply(
            "⚠️ Could not extract text from image, but I saved the image."
          );
        }
      } else if (ctx.message?.document) {
        content = ctx.message.document.file_name || "Document uploaded";

        await getConvex().action(api.sessions.addSessionData, {
          sessionId: activeSession._id,
          messageId,
          type: "document",
          content,
        });
      }

      // Schedule timeout check instead of immediate prompt
      scheduleTimeoutCheck(ctx.from?.id.toString() || "", activeSession._id);
    }
  );

  // REMOVED: confirm_contact callback - now using auto-save (contacts save automatically)

  bot.callbackQuery("cancel_contact", async (ctx: BotContext) => {
    await ctx.answerCallbackQuery();

    const telegramUserId = ctx.from?.id.toString();
    if (!telegramUserId) return;

    const activeSession = await getConvex().query(
      api.sessions.getActiveSession,
      {
        telegramUserId,
      }
    );

    if (activeSession) {
      await getConvex().mutation(api.sessions.closeSession, {
        sessionId: activeSession._id,
      });

      // Clear tracking data
      const sessionKey = `${ctx.from?.id}_${activeSession._id}`;
      sessionContacts.delete(sessionKey);
      sessionProcessedCursors.delete(sessionKey);
      processedImageIds.delete(sessionKey);
      autoSaveLastRun.delete(sessionKey);
      organizedContactData.delete(sessionKey);

      // Clear any pending timeout for this user
      if (activeTimeouts.has(telegramUserId)) {
        clearTimeout(activeTimeouts.get(telegramUserId)!);
        activeTimeouts.delete(telegramUserId);
      }
    }

    await ctx.editMessageText(
      "❌ Contact collection cancelled.\n\nUse /contact to start a new session."
    );
  });

  // REMOVED: done_yes/done_no callbacks - now using auto-processing
  bot.callbackQuery(["done_yes", "done_no"], async (ctx: BotContext) => {
    await ctx.answerCallbackQuery();
    await ctx.editMessageText("🔄 This bot now auto-saves contacts! No manual confirmation needed.");
  });


  // REMOVED: continue_adding callback - not needed with auto-save
  bot.callbackQuery("continue_adding", async (ctx: BotContext) => {
    await ctx.answerCallbackQuery();
    await ctx.editMessageText("📝 Continue sending contact information! Contacts save automatically.");
  });
}

async function scheduleTimeoutCheck(telegramUserId: string, sessionId: string) {
  // Clear any existing timeout for this user
  if (activeTimeouts.has(telegramUserId)) {
    clearTimeout(activeTimeouts.get(telegramUserId)!);
  }

  const timeout = setTimeout(async () => {
    try {
      // Verify the session is still active and matches the expected session ID
      const session = await getConvex().query(api.sessions.getActiveSession, {
        telegramUserId,
      });

      // Auto-process and close session after 30 seconds of inactivity
      if (session && session.isActive && session._id === sessionId) {
        const currentBot = getBot();
        const sessionKey = `${telegramUserId}_${session._id}`;

        try {
          // Get sales rep info
          const salesRep = await getConvex().query(
            api.salesReps.getSalesRepByTelegramId,
            { telegramId: telegramUserId }
          );

          if (salesRep) {
            const salesRepName = `${salesRep.firstName} ${salesRep.lastName || ""}`.trim();

            // Try to save any remaining unsaved contact data
            await autoSaveContact(
              { from: { id: parseInt(telegramUserId) }, reply: (msg: string) => currentBot.api.sendMessage(telegramUserId, msg) } as any,
              session,
              session.salesRepId,
              salesRepName
            );
          }

          // Get final count of saved contacts
          const savedContacts = sessionContacts.get(sessionKey) || [];
          const contactCount = savedContacts.length;

          // Close the session
          await getConvex().mutation(api.sessions.closeSession, {
            sessionId: session._id,
          });

          // Clear tracking data
          sessionContacts.delete(sessionKey);
          sessionProcessedCursors.delete(sessionKey);
          processedImageIds.delete(sessionKey);
          autoSaveLastRun.delete(sessionKey);
          organizedContactData.delete(sessionKey);

          // Send completion summary
          if (contactCount > 0) {
            await currentBot.api.sendMessage(
              telegramUserId,
              `✅ **Session Complete!**

📊 Successfully saved **${contactCount}** contact${contactCount !== 1 ? 's' : ''}
⏰ Session auto-closed after 30 seconds

Use /contact to start collecting more contacts.`
            );
          } else {
            await currentBot.api.sendMessage(
              telegramUserId,
              `⏰ **Session Timeout**

No contacts were saved. Make sure to include:
• Name
• Email or phone number

Use /contact to try again.`
            );
          }
        } catch (error) {
          console.error('Error in auto-close:', error);
          await currentBot.api.sendMessage(
            telegramUserId,
            "❌ Session closed due to timeout. Use /contact to start again."
          );
        }
      }
      // If session is no longer active or doesn't match, silently clean up the timeout
      activeTimeouts.delete(telegramUserId);
    } catch (error) {
      console.error("Error in timeout check:", error);
      // Clean up on error
      activeTimeouts.delete(telegramUserId);
    }
  }, 30000); // 30 seconds

  activeTimeouts.set(telegramUserId, timeout);
}

