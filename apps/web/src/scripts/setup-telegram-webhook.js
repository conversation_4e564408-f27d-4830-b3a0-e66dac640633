#!/usr/bin/env node

const TELEGRAM_BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN;
const WEBHOOK_URL =
  process.env.WEBHOOK_URL ||
  "https://contact.buddytools.org/api/telegram/webhook";

if (!TELEGRAM_BOT_TOKEN) {
  console.error("❌ TELEGRAM_BOT_TOKEN environment variable is required");
  process.exit(1);
}

async function registerCommands() {
  try {
    console.log("🔧 Registering bot commands...");

    const commands = [
      {
        command: "start",
        description: "Welcome message and bot introduction",
      },
      {
        command: "contact",
        description: "Start collecting contact information",
      },
      {
        command: "info",
        description: "Learn how the bot works",
      },
      {
        command: "help",
        description: "Show help and usage instructions",
      },
    ];

    const response = await fetch(
      `https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/setMyCommands`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          commands,
          scope: { type: "default" },
          language_code: "en",
        }),
      }
    );

    const result = await response.json();

    if (result.ok) {
      console.log("✅ Commands registered successfully!");
      console.log("📝 Registered commands:");
      commands.forEach((cmd) => {
        console.log(`   /${cmd.command} - ${cmd.description}`);
      });
    } else {
      console.error("❌ Failed to register commands:", result.description);
    }
  } catch (error) {
    console.error("❌ Error registering commands:", error.message);
  }
}

async function setupWebhook() {
  try {
    console.log("🔧 Setting up Telegram webhook...");

    const response = await fetch(
      `https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/setWebhook`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          url: WEBHOOK_URL,
          allowed_updates: ["message", "callback_query"],
        }),
      }
    );

    const result = await response.json();

    if (result.ok) {
      console.log("✅ Webhook set successfully!");
      console.log(`📡 Webhook URL: ${WEBHOOK_URL}`);
    } else {
      console.error("❌ Failed to set webhook:", result.description);
    }

    const infoResponse = await fetch(
      `https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/getWebhookInfo`
    );
    const webhookInfo = await infoResponse.json();

    if (webhookInfo.ok) {
      console.log("\n📊 Current webhook info:");
      console.log(`URL: ${webhookInfo.result.url}`);
      console.log(
        `Pending updates: ${webhookInfo.result.pending_update_count}`
      );
      console.log(
        `Last error: ${webhookInfo.result.last_error_message || "None"}`
      );
    }

    // Also register commands when setting up webhook
    await registerCommands();
  } catch (error) {
    console.error("❌ Error setting up webhook:", error.message);
  }
}

async function deleteWebhook() {
  try {
    console.log("🗑️  Deleting Telegram webhook...");

    const response = await fetch(
      `https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/deleteWebhook`,
      {
        method: "POST",
      }
    );

    const result = await response.json();

    if (result.ok) {
      console.log("✅ Webhook deleted successfully!");
    } else {
      console.error("❌ Failed to delete webhook:", result.description);
    }
  } catch (error) {
    console.error("❌ Error deleting webhook:", error.message);
  }
}

const command = process.argv[2];

if (command === "set") {
  setupWebhook();
} else if (command === "delete") {
  deleteWebhook();
} else if (command === "commands") {
  registerCommands();
} else {
  console.log(`
🤖 Telegram Webhook Setup Script

Usage:
  node setup-telegram-webhook.js set      - Set webhook and register commands
  node setup-telegram-webhook.js delete   - Delete webhook
  node setup-telegram-webhook.js commands - Register bot commands only

Environment variables required:
  TELEGRAM_BOT_TOKEN - Your bot token from @BotFather
  WEBHOOK_URL        - Your webhook URL (optional, defaults to contact.buddytools.org)
  `);
}
