#!/usr/bin/env node

interface SendMessageResponse {
  ok: boolean;
  result?: {
    message_id: number;
    date: number;
    chat: {
      id: number;
      type: string;
    };
    text?: string;
  };
  description?: string;
}

const BOT_TOKEN: string | undefined = process.env.TELEGRAM_BOT_TOKEN;
const CHAT_ID: string | undefined = process.env.TEST_CHAT_ID;

if (!BOT_TOKEN) {
  console.error("❌ TELEGRAM_BOT_TOKEN environment variable is required");
  process.exit(1);
}

if (!CHAT_ID) {
  console.error("❌ TEST_CHAT_ID environment variable is required");
  console.log("💡 Get your chat ID by messaging @userinfobot on Telegram");
  process.exit(1);
}

async function sendTestMessage(message: string): Promise<void> {
  try {
    console.log(`📤 Sending test message: "${message}"`);

    const response = await fetch(
      `https://api.telegram.org/bot${BOT_TOKEN}/sendMessage`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          chat_id: CHAT_ID,
          text: message,
        }),
      }
    );

    const result: SendMessageResponse = await response.json();

    if (result.ok) {
      console.log("✅ Message sent successfully!");
      console.log(`📨 Message ID: ${result.result?.message_id}`);
      console.log(
        `🕒 Sent at: ${new Date((result.result?.date || 0) * 1000).toISOString()}`
      );
    } else {
      console.error("❌ Failed to send message:", result.description);
    }
  } catch (error) {
    console.error("❌ Error sending message:", (error as Error).message);
  }
}

async function runTests(): Promise<void> {
  console.log("🧪 Starting Telegram bot message tests...\n");

  // Test 1: Basic message
  await sendTestMessage("Hello! This is a test message.");
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Test 2: /start command
  await sendTestMessage("/start");
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Test 3: /help command
  await sendTestMessage("/help");
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Test 4: /info command
  await sendTestMessage("/info");
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Test 5: /contact command
  await sendTestMessage("/contact");
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Test 6: Random text (should not trigger any command)
  await sendTestMessage("This is just random text to test message handling");

  console.log("\n✅ All test messages sent!");
  console.log("💡 Check your Telegram chat to see bot responses");
  console.log("💡 Run: bun run test:webhook to check webhook status");
  console.log(
    "💡 Run: bun run test:updates to see if messages reached the bot"
  );
}

runTests();
