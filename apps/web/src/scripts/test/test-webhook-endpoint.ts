#!/usr/bin/env node

// Mock Telegram update for testing webhook endpoint
interface MockTelegramUpdate {
  update_id: number;
  message: {
    message_id: number;
    from: {
      id: number;
      is_bot: boolean;
      first_name: string;
      last_name?: string;
      username?: string;
    };
    chat: {
      id: number;
      type: string;
    };
    date: number;
    text: string;
  };
}

const WEBHOOK_ENDPOINT: string =
  process.env.WEBHOOK_URL || "http://localhost:3001/api/telegram/webhook";

async function testWebhookEndpoint(update: MockTelegramUpdate): Promise<void> {
  try {
    console.log(`🧪 Testing webhook endpoint: ${WEBHOOK_ENDPOINT}`);
    console.log("📤 Sending mock update:", JSON.stringify(update, null, 2));

    const response = await fetch(WEBHOOK_ENDPOINT, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "User-Agent": "TelegramBot/1.0",
      },
      body: JSON.stringify(update),
    });

    console.log(
      `📨 Response Status: ${response.status} ${response.statusText}`
    );

    const responseText = await response.text();
    console.log("📋 Response Body:", responseText);

    if (response.ok) {
      console.log("✅ Webhook endpoint is working!");
    } else {
      console.log("❌ Webhook endpoint returned an error");
    }
  } catch (error) {
    console.error(
      "❌ Error testing webhook endpoint:",
      (error as Error).message
    );
    console.log("💡 Make sure your local server is running on port 3001");
  }
}

async function testGetEndpoint(): Promise<void> {
  try {
    console.log(`🔍 Testing GET endpoint: ${WEBHOOK_ENDPOINT}`);

    const response = await fetch(WEBHOOK_ENDPOINT, {
      method: "GET",
    });

    console.log(
      `📨 GET Response Status: ${response.status} ${response.statusText}`
    );

    const responseText = await response.text();
    console.log("📋 GET Response Body:", responseText);
  } catch (error) {
    console.error("❌ Error testing GET endpoint:", (error as Error).message);
  }
}

async function runEndpointTests(): Promise<void> {
  console.log("🧪 Starting Webhook Endpoint Tests...\n");

  // Test 1: GET endpoint
  await testGetEndpoint();
  console.log("\n" + "=".repeat(50) + "\n");

  // Test 2: /start command
  const startUpdate: MockTelegramUpdate = {
    update_id: 123_456_789,
    message: {
      message_id: 1,
      from: {
        id: 987_654_321,
        is_bot: false,
        first_name: "Test",
        last_name: "User",
        username: "testuser",
      },
      chat: {
        id: 987_654_321,
        type: "private",
      },
      date: Math.floor(Date.now() / 1000),
      text: "/start",
    },
  };

  await testWebhookEndpoint(startUpdate);
  console.log("\n" + "=".repeat(50) + "\n");

  // Test 3: Regular message
  const textUpdate: MockTelegramUpdate = {
    update_id: 123_456_790,
    message: {
      message_id: 2,
      from: {
        id: 987_654_321,
        is_bot: false,
        first_name: "Test",
        last_name: "User",
        username: "testuser",
      },
      chat: {
        id: 987_654_321,
        type: "private",
      },
      date: Math.floor(Date.now() / 1000),
      text: "Hello bot! This is a test message.",
    },
  };

  await testWebhookEndpoint(textUpdate);
  console.log("\n" + "=".repeat(50) + "\n");

  // Test 4: /contact command
  const contactUpdate: MockTelegramUpdate = {
    update_id: 123_456_791,
    message: {
      message_id: 3,
      from: {
        id: 987_654_321,
        is_bot: false,
        first_name: "Test",
        last_name: "User",
        username: "testuser",
      },
      chat: {
        id: 987_654_321,
        type: "private",
      },
      date: Math.floor(Date.now() / 1000),
      text: "/contact",
    },
  };

  await testWebhookEndpoint(contactUpdate);

  console.log("\n✅ Webhook endpoint tests completed!");
  console.log("\n💡 Usage:");
  console.log(
    "  WEBHOOK_URL=http://localhost:3001/api/telegram/webhook tsx test-webhook-endpoint.ts"
  );
  console.log(
    "  WEBHOOK_URL=https://contact.buddytools.org/api/telegram/webhook tsx test-webhook-endpoint.ts"
  );
  console.log("\n🔧 To test locally:");
  console.log("  1. Run: bun run dev (start local server)");
  console.log("  2. Run this script with localhost URL");
  console.log("  3. Check console logs in dev server");
}

runEndpointTests();
