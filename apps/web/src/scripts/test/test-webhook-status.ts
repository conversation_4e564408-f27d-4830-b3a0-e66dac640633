#!/usr/bin/env node

interface WebhookInfo {
  url: string;
  has_custom_certificate: boolean;
  pending_update_count: number;
  last_error_date?: number;
  last_error_message?: string;
  max_connections?: number;
  allowed_updates?: string[];
}

interface TelegramResponse {
  ok: boolean;
  result: WebhookInfo;
  description?: string;
}

const TELEGRAM_BOT_TOKEN: string | undefined = process.env.TELEGRAM_BOT_TOKEN;

if (!TELEGRAM_BOT_TOKEN) {
  console.error("❌ TELEGRAM_BOT_TOKEN environment variable is required");
  process.exit(1);
}

async function checkWebhookStatus(): Promise<void> {
  try {
    console.log("🔍 Checking Telegram webhook status...\n");

    const response = await fetch(
      `https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/getWebhookInfo`
    );
    const result: TelegramResponse = await response.json();

    if (result.ok) {
      const info = result.result;

      console.log("📊 Webhook Information:");
      console.log(`✅ Status: ${info.url ? "ACTIVE" : "NOT SET"}`);
      console.log(`🌐 URL: ${info.url || "None"}`);
      console.log(`📬 Pending Updates: ${info.pending_update_count}`);
      console.log(
        `🕒 Last Error Date: ${info.last_error_date ? new Date(info.last_error_date * 1000).toISOString() : "None"}`
      );
      console.log(
        `❌ Last Error Message: ${info.last_error_message || "None"}`
      );
      console.log(
        `📜 Allowed Updates: ${info.allowed_updates ? info.allowed_updates.join(", ") : "All"}`
      );

      if (info.max_connections) {
        console.log(`🔗 Max Connections: ${info.max_connections}`);
      }

      // Check if webhook is properly configured
      if (!info.url) {
        console.log(
          "\n⚠️  WARNING: No webhook URL set. Bot will not receive updates."
        );
        console.log("💡 Run: bun run webhook:setup to set webhook");
      } else if (
        info.url !== "https://contact.buddytools.org/api/telegram/webhook"
      ) {
        console.log(
          "\n⚠️  WARNING: Webhook URL is not pointing to production domain"
        );
        console.log(`   Current: ${info.url}`);
        console.log(
          "   Expected: https://contact.buddytools.org/api/telegram/webhook"
        );
      }

      if (info.pending_update_count > 0) {
        console.log(
          `\n📨 There are ${info.pending_update_count} pending updates!`
        );
        console.log("💡 Run: bun run test:updates to see them");
      }

      if (info.last_error_message) {
        console.log(`\n❌ Last Error: ${info.last_error_message}`);
        console.log("💡 Check your webhook endpoint and SSL certificate");
      }
    } else {
      console.error("❌ Failed to get webhook info:", result.description);
    }
  } catch (error) {
    console.error(
      "❌ Error checking webhook status:",
      (error as Error).message
    );
  }
}

checkWebhookStatus();
