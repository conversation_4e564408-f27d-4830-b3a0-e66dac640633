#!/usr/bin/env node

interface TelegramUpdate {
  update_id: number;
  message?: {
    message_id: number;
    from: {
      id: number;
      is_bot: boolean;
      first_name: string;
      last_name?: string;
      username?: string;
    };
    chat: {
      id: number;
      type: string;
    };
    date: number;
    text?: string;
    photo?: Array<{
      file_id: string;
      file_unique_id: string;
      file_size?: number;
      width: number;
      height: number;
    }>;
    document?: {
      file_id: string;
      file_unique_id: string;
      file_name?: string;
      mime_type?: string;
      file_size?: number;
    };
  };
  callback_query?: {
    id: string;
    from: {
      id: number;
      is_bot: boolean;
      first_name: string;
    };
    message?: any;
    data?: string;
  };
}

interface GetUpdatesResponse {
  ok: boolean;
  result: TelegramUpdate[];
  description?: string;
}

const TELEGRAM_TOKEN: string | undefined = process.env.TELEGRAM_BOT_TOKEN;

if (!TELEGRAM_TOKEN) {
  console.error("❌ TELEGRAM_BOT_TOKEN environment variable is required");
  process.exit(1);
}

async function getUpdates(offset?: number): Promise<void> {
  try {
    console.log("📨 Fetching updates from Telegram...\n");

    const url = `https://api.telegram.org/bot${TELEGRAM_TOKEN}/getUpdates${offset ? `?offset=${offset}` : ""}`;
    const response = await fetch(url);
    const result: GetUpdatesResponse = await response.json();

    if (result.ok) {
      const updates = result.result;

      if (updates.length === 0) {
        console.log("📭 No pending updates found");
        return;
      }

      console.log(`📨 Found ${updates.length} update(s):\n`);

      updates.forEach((update, index) => {
        console.log(`--- Update ${index + 1} (ID: ${update.update_id}) ---`);

        if (update.message) {
          const msg = update.message;
          console.log(
            `👤 From: ${msg.from.first_name} ${msg.from.last_name || ""} (@${msg.from.username || "no username"})`
          );
          console.log(`💬 Chat ID: ${msg.chat.id}`);
          console.log(`🕒 Date: ${new Date(msg.date * 1000).toISOString()}`);

          if (msg.text) {
            console.log(`📝 Text: "${msg.text}"`);
          }

          if (msg.photo) {
            console.log(`📸 Photo: ${msg.photo.length} sizes available`);
          }

          if (msg.document) {
            console.log(`📎 Document: ${msg.document.file_name || "unnamed"}`);
          }
        }

        if (update.callback_query) {
          const callback = update.callback_query;
          console.log(`🔘 Callback Query from: ${callback.from.first_name}`);
          console.log(`📊 Data: ${callback.data || "none"}`);
        }

        console.log(""); // Empty line between updates
      });

      // Show the highest update_id for next polling
      const highestId = Math.max(...updates.map((u) => u.update_id));
      console.log(
        `💡 To acknowledge these updates, use offset: ${highestId + 1}`
      );
      console.log(
        "⚠️  WARNING: getUpdates will interfere with webhook. Delete webhook first if needed."
      );
    } else {
      console.error("❌ Failed to get updates:", result.description);
    }
  } catch (error) {
    console.error("❌ Error fetching updates:", (error as Error).message);
  }
}

async function main(): Promise<void> {
  const offset = process.argv[2] ? Number.parseInt(process.argv[2]) : undefined;

  if (offset) {
    console.log(`📍 Using offset: ${offset}\n`);
  }

  await getUpdates(offset);

  console.log("\n🔧 Usage:");
  console.log("  tsx test-get-updates.ts        # Get all pending updates");
  console.log("  tsx test-get-updates.ts 123    # Get updates with offset 123");
  console.log("\n💡 Tips:");
  console.log("  - Updates show messages sent to the bot");
  console.log("  - If webhook is active, updates might be empty");
  console.log("  - Use offset to acknowledge processed updates");
}

main();
