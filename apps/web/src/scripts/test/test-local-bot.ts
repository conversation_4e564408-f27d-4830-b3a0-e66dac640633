#!/usr/bin/env node

import type { Context } from "grammy";
import { <PERSON><PERSON> } from "grammy";

// Use polling instead of webhooks for local testing
const LOCAL_TOKEN: string | undefined = process.env.TELEGRAM_BOT_TOKEN;

if (!LOCAL_TOKEN) {
  console.error("❌ TELEGRAM_BOT_TOKEN environment variable is required");
  console.log("💡 Set it in your .env file");
  process.exit(1);
}

// Create bot instance
const localBot = new Bot(LOCAL_TOKEN);

// Simple handlers for testing (without Convex dependencies)
localBot.command("start", async (ctx: Context) => {
  console.log(
    `📨 Received /start from ${ctx.from?.first_name} (${ctx.from?.id})`
  );

  await ctx.reply(`👋 Welcome to BuddyContact Local Test!

🔧 This is the local testing version running in polling mode.

Available commands:
/contact - Start collecting contact information
/info - Learn how the bot works
/help - Show this help message

🧪 Testing Features:
• Direct console output for debugging
• No webhook required
• Instant responses
• Full error logging

Send me any message to test the bot functionality!`);
});

localBot.command("help", async (ctx: Context) => {
  console.log(`📨 Received /help from ${ctx.from?.first_name}`);

  await ctx.reply(`🆘 BuddyContact Local Test Help

🧪 Test Commands:
• /start - Welcome message
• /help - This help message
• /info - Detailed bot information
• /contact - Contact collection flow
• /ping - Test response

📊 Debug Info:
• Running in polling mode
• No webhook required
• Direct console logging
• Instant message processing

This is for local testing only. Production uses webhook mode.`);
});

localBot.command("info", async (ctx: Context) => {
  console.log(`📨 Received /info from ${ctx.from?.first_name}`);

  await ctx.reply(`🤖 BuddyContact - Local Test Mode

🔧 **What I Do:**
I help sales representatives collect and organize contact information from leads using AI technology.

⚡ **Local Test Features:**
• Polling mode (no webhook needed)
• Console logging for debugging
• Instant message processing
• Error reporting in terminal
• Full functionality testing

🚀 **Production Features:**
• AI-powered contact extraction
• Image processing (business cards, QR codes)
• Session management with timeouts
• Excel export functionality
• Comprehensive data storage

💡 This is the LOCAL TESTING version. The production bot uses webhooks at contact.buddytools.org`);
});

localBot.command("contact", async (ctx: Context) => {
  console.log(`📨 Received /contact from ${ctx.from?.first_name}`);

  await ctx.reply(`🔄 Contact Collection Started!

⚠️  **LOCAL TEST MODE**
This is a simplified version for testing.

In production, this command:
✅ Creates a session in Convex
✅ Processes multiple inputs (text, images, docs)
✅ Uses AI to extract contact information
✅ Provides 3-second timeout mechanism
✅ Shows organized contact preview
✅ Saves confirmed contacts to database

🧪 **For local testing:**
Send me contact information and I'll echo it back to confirm the bot is responding properly.

Try sending:
• A name and phone number
• Some business details
• Any text message`);
});

localBot.command("ping", async (ctx: Context) => {
  console.log(`📨 Received /ping from ${ctx.from?.first_name}`);
  const startTime = Date.now();

  await ctx.reply("🏓 Pong!");

  const endTime = Date.now();
  console.log(`⚡ Response time: ${endTime - startTime}ms`);
});

// Handle all text messages
localBot.on("message:text", async (ctx: Context) => {
  const text = ctx.message?.text;
  console.log(
    `📨 Received text message from ${ctx.from?.first_name}: "${text}"`
  );

  if (!text?.startsWith("/")) {
    await ctx.reply(`✅ Message received: "${text}"

🧪 **Local Test Confirmation**
Your message was processed successfully!

In production mode, this message would:
• Be logged to Convex database
• Trigger AI processing if in a /contact session
• Be analyzed for contact information
• Be part of the session management flow

The bot is working correctly! ✨`);
  }
});

// Handle photos
localBot.on("message:photo", async (ctx: Context) => {
  console.log(`📨 Received photo from ${ctx.from?.first_name}`);

  await ctx.reply(`📸 Photo received!

🧪 **Local Test Mode**
In production, this would:
• Download and compress the image
• Use AI (Gemini 2.0 Flash) to extract text
• Process business cards and QR codes
• Add extracted data to contact session

Your image upload is working correctly! ✨`);
});

// Handle documents
localBot.on("message:document", async (ctx: Context) => {
  const fileName = ctx.message?.document?.file_name;
  console.log(`📨 Received document from ${ctx.from?.first_name}: ${fileName}`);

  await ctx.reply(`📎 Document received: ${fileName || "unnamed file"}

🧪 **Local Test Mode**
In production, this would be processed for contact information.

Your document upload is working correctly! ✨`);
});

// Error handler
localBot.catch((err) => {
  console.error("❌ Bot error:", err);
});

// Start the bot
async function startLocalBot(): Promise<void> {
  try {
    console.log("🚀 Starting BuddyContact Local Test Bot...");
    console.log("🔄 Running in polling mode (no webhook required)");
    console.log("💡 Send /start to your bot to test functionality");
    console.log("⚠️  Press Ctrl+C to stop\n");

    // Start polling
    await localBot.start();
  } catch (error) {
    console.error("❌ Failed to start local bot:", (error as Error).message);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on("SIGINT", async () => {
  console.log("\n🛑 Stopping local bot...");
  await localBot.stop();
  console.log("✅ Bot stopped successfully");
  process.exit(0);
});

startLocalBot();
