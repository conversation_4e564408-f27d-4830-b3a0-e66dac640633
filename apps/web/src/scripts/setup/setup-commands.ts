#!/usr/bin/env node

import "dotenv/config";
import { Bo<PERSON> } from "grammy";

const TELEGRAM_BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN;

if (!TELEGRAM_BOT_TOKEN) {
  console.error("❌ TELEGRAM_BOT_TOKEN environment variable is required");
  process.exit(1);
}

async function setupBotCommands() {
  try {
    console.log("🤖 Setting up Telegram bot commands...");

    const bot = new Bot(TELEGRAM_BOT_TOKEN!);

    const commands = [
      {
        command: "start",
        description: "Start the bot and see welcome message",
      },
      {
        command: "contact",
        description: "Start collecting contact information",
      },
      {
        command: "cancel",
        description: "Cancel current contact session",
      },
      {
        command: "close",
        description: "Close current contact session",
      },
      {
        command: "help",
        description: "Show help and available commands",
      },
      {
        command: "info",
        description: "Learn how the bot works",
      },
    ];

    await bot.api.setMyCommands(commands);

    console.log("✅ Bot commands registered successfully:");
    commands.forEach((cmd) => {
      console.log(`   /${cmd.command} - ${cmd.description}`);
    });
  } catch (error) {
    console.error("❌ Error setting up bot commands:", error);
    process.exit(1);
  }
}

setupBotCommands();
