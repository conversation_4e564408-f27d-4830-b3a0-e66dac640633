import { webhookCallback } from "grammy";
import { type NextRequest, NextResponse } from "next/server";
import { getBot, setupBotMiddleware } from "../../../../lib/telegram/bot";
import { setupBotHandlers } from "../../../../lib/telegram/handlers";
import {
  logWebhookError,
  logWebhookUpdate,
} from "../../../../lib/utils/webhook-logger";

// Initialize bot setup lazily to prevent build-time errors
let isInitialized = false;

function ensureBotSetup() {
  if (!isInitialized) {
    const bot = getBot();
    setupBotMiddleware();
    setupBotHandlers();
    isInitialized = true;
    return bot;
  }
  return getBot();
}

export async function POST(request: NextRequest) {
  try {
    // Parse body once and store for reuse
    const clonedForLogging = request.clone();
    const body = await clonedForLogging.json().catch(() => null);

    if (body) {
      logWebhookUpdate(body, "received");
    }

    // Initialize bot and handlers at runtime
    const bot = ensureBotSetup();
    const handleUpdate = webhookCallback(bot, "std/http");

    // Clone request for processing to avoid body consumption issues
    const clonedForProcessing = request.clone();

    // Add timeout protection for long-running operations
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error("Webhook timeout")), 9000); // 9 seconds
    });

    try {
      // Race between processing and timeout
      const response = await Promise.race([
        handleUpdate(clonedForProcessing),
        timeoutPromise
      ]) as Response | void;

      return response || new NextResponse("OK", { status: 200 });
    } catch (timeoutError: any) {
      if (timeoutError?.message === "Webhook timeout") {
        // Process in background with a fresh clone
        const clonedForBackground = request.clone();
        handleUpdate(clonedForBackground).catch(error => {
          console.error("Background processing failed:", error);
        });

        return new NextResponse("Processing in background", { status: 200 });
      }
      throw timeoutError;
    }
  } catch (error) {
    // Log error details server-side only
    logWebhookError(error, "handling");

    // Return generic error message to client (no internal details)
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

export async function GET() {
  return new NextResponse("Telegram webhook endpoint is active", {
    status: 200,
  });
}
