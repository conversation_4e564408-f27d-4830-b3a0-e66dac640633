import { api } from "@BuddyContact/backend/convex/_generated/api";
import type { Id } from "@BuddyContact/backend/convex/_generated/dataModel";
import { ConvexHttpClient } from "convex/browser";
import ExcelJs from "exceljs";
import { type NextRequest, NextResponse } from "next/server";

let convex: ConvexHttpClient | null = null;

function getConvex() {
  if (!convex) {
    const url =
      process.env.NEXT_PUBLIC_CONVEX_URL ||
      "https://dummy-url-for-build.convex.cloud";
    convex = new ConvexHttpClient(url);
  }
  return convex;
}

interface SalesRepInfo {
  firstName: string;
  lastName?: string;
}

function isSalesRepInfo(obj: unknown): obj is SalesRepInfo {
  return (
    typeof obj === "object" &&
    obj !== null &&
    "firstName" in obj &&
    typeof (obj as any).firstName === "string"
  );
}

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const salesRepId = searchParams.get("salesRepId");
    const format = searchParams.get("format") || "xlsx";
    const includeUnconfirmed =
      searchParams.get("includeUnconfirmed") === "true";

    let contacts;
    if (salesRepId) {
      contacts = await getConvex().query(api.contacts.getContactsBySalesRep, {
        salesRepId: salesRepId as Id<"salesReps">,
      });
    } else {
      contacts = await getConvex().query(
        api.contacts.getAllContactsWithSalesRep
      );
    }

    const filteredContacts = includeUnconfirmed
      ? contacts
      : contacts.filter((contact: any) => contact.isConfirmed);

    const workbook = new ExcelJs.Workbook();
    const worksheet = workbook.addWorksheet("Contacts");

    worksheet.columns = [
      { header: "Sales Person", key: "salesPerson", width: 20 },
      { header: "Lead Name", key: "leadName", width: 25 },
      { header: "Lead Website", key: "leadWebsite", width: 30 },
      { header: "General Information", key: "generalInfo", width: 40 },
      { header: "Phone", key: "phone", width: 20 },
      { header: "Telegram Handle", key: "telegram", width: 20 },
      { header: "Date Added", key: "dateAdded", width: 15 },
      { header: "Confirmed", key: "confirmed", width: 10 },
    ];

    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE6E6FA" },
    };

    for (const contact of filteredContacts) {
      const salesRep = "salesRep" in contact ? contact.salesRep : null;
      const salesPersonName = isSalesRepInfo(salesRep)
        ? `${salesRep.firstName} ${salesRep.lastName || ""}`.trim()
        : "Unknown";

      worksheet.addRow({
        salesPerson: salesPersonName,
        leadName: contact.leadName || "Not provided",
        leadWebsite: contact.leadWebsite || "Not provided",
        generalInfo: contact.leadGeneralInfo || "Not provided",
        phone: contact.leadPhone || "Not provided",
        telegram: contact.leadTelegram || "Not provided",
        dateAdded: new Date(contact.extractedAt).toLocaleDateString(),
        confirmed: contact.isConfirmed ? "Yes" : "No",
      });
    }

    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) {
        row.alignment = { vertical: "middle", wrapText: true };
      }
    });

    const buffer = await workbook.xlsx.writeBuffer();

    const filename = `contacts-${new Date().toISOString().split("T")[0]}.xlsx`;

    return new NextResponse(buffer, {
      status: 200,
      headers: {
        "Content-Type":
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "Content-Disposition": `attachment; filename="${filename}"`,
        "Content-Length": buffer.byteLength.toString(),
      },
    });
  } catch (error) {
    console.error("Error generating Excel export:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to generate Excel file" }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { contactIds, filename } = body;

    if (!(contactIds && Array.isArray(contactIds))) {
      return new NextResponse(
        JSON.stringify({ error: "Invalid contact IDs provided" }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    const allContacts = await getConvex().query(
      api.contacts.getAllContactsWithSalesRep
    );
    const selectedContacts = allContacts.filter((contact) =>
      contactIds.includes(contact._id)
    );

    const workbook = new ExcelJs.Workbook();
    const worksheet = workbook.addWorksheet("Selected Contacts");

    worksheet.columns = [
      { header: "Sales Person", key: "salesPerson", width: 20 },
      { header: "Lead Name", key: "leadName", width: 25 },
      { header: "Lead Website", key: "leadWebsite", width: 30 },
      { header: "General Information", key: "generalInfo", width: 40 },
      { header: "Phone", key: "phone", width: 20 },
      { header: "Telegram Handle", key: "telegram", width: 20 },
      { header: "Date Added", key: "dateAdded", width: 15 },
      { header: "Raw Data", key: "rawData", width: 50 },
    ];

    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE6E6FA" },
    };

    for (const contact of selectedContacts) {
      const salesRep = "salesRep" in contact ? contact.salesRep : null;
      const salesPersonName = isSalesRepInfo(salesRep)
        ? `${salesRep.firstName} ${salesRep.lastName || ""}`.trim()
        : "Unknown";

      worksheet.addRow({
        salesPerson: salesPersonName,
        leadName: contact.leadName || "Not provided",
        leadWebsite: contact.leadWebsite || "Not provided",
        generalInfo: contact.leadGeneralInfo || "Not provided",
        phone: contact.leadPhone || "Not provided",
        telegram: contact.leadTelegram || "Not provided",
        dateAdded: new Date(contact.extractedAt).toLocaleDateString(),
        rawData: contact.rawData || "",
      });
    }

    const buffer = await workbook.xlsx.writeBuffer();
    const exportFilename =
      filename ||
      `selected-contacts-${new Date().toISOString().split("T")[0]}.xlsx`;

    return new NextResponse(buffer, {
      status: 200,
      headers: {
        "Content-Type":
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "Content-Disposition": `attachment; filename="${exportFilename}"`,
        "Content-Length": buffer.byteLength.toString(),
      },
    });
  } catch (error) {
    console.error("Error generating custom Excel export:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to generate custom Excel file" }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}
