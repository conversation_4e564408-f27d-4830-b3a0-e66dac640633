{"name": "fumadocs", "version": "0.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev --turbo --port=4000", "start": "next start", "postinstall": "fumadocs-mdx"}, "dependencies": {"next": "15.5.4", "react": "^19.1.1", "react-dom": "^19.1.1", "fumadocs-ui": "15.8.0", "fumadocs-core": "15.8.0", "fumadocs-mdx": "12.0.1"}, "devDependencies": {"@types/node": "24.5.2", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "typescript": "^5.9.2", "@types/mdx": "^2.0.13", "@tailwindcss/postcss": "^4.1.13", "tailwindcss": "^4.1.13", "postcss": "^8.5.6"}}