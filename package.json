{"name": "BuddyContact", "private": true, "type": "module", "workspaces": ["apps/*", "packages/*"], "scripts": {"check": "biome check --write .", "dev": "turbo dev", "build": "turbo build", "check-types": "turbo check-types", "dev:native": "turbo -F native dev", "dev:web": "turbo -F web dev", "dev:server": "turbo -F @BuddyContact/backend dev", "dev:setup": "turbo -F @BuddyContact/backend dev:setup"}, "dependencies": {}, "devDependencies": {"@biomejs/biome": "2.2.4", "shadcn": "^3.3.1", "turbo": "^2.5.4", "ultracite": "5.4.4"}, "packageManager": "bun@1.2.19"}